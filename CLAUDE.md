# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Core Development
- `npm run dev` - Start development server (default development mode)
- `npm run dev:local` - Start development server with local environment
- `npm run dev:remote` - Start development server with production environment
- `npm run build` - Build for production
- `npm run build:dev` - Build for development
- `npm run lint` - Run ESLint
- `npm run preview` - Preview production build

### Testing
- `npm run test` - Run tests (configured to run src/working.test.js)
- `npm run test:watch` - Run tests in watch mode
- `npm run test:coverage` - Run tests with coverage report
- `npm run test:generate` - Generate test files using scripts/generate-test.js

### Supabase
- `npm run supabase:start` - Start local Supabase instance
- `npm run supabase:stop` - Stop local Supabase instance
- `npm run supabase:status` - Check Supabase status
- `npm run supabase:types` - Generate TypeScript types from remote schema
- `npm run supabase:types:local` - Generate TypeScript types from local schema

## Architecture Overview

### Tech Stack
- **Frontend**: React 18 + TypeScript + Vite
- **Routing**: React Router (HashRouter for PWA compatibility)
- **State Management**: TanStack Query (React Query) for server state
- **UI Components**: Radix UI + shadcn/ui + Tailwind CSS
- **Backend**: Supabase (PostgreSQL + Auth + Storage + Edge Functions)
- **Testing**: Jest + React Testing Library

### Key Architecture Patterns

#### Data Layer
- **Supabase Integration**: Primary data source with client in `src/integrations/supabase/client.ts`
- **Query Management**: TanStack Query configured in `src/providers/AppProviders.tsx` with:
  - Aggressive refresh strategy (`refetchOnWindowFocus: 'always'`)
  - 5-minute stale time, 30-minute garbage collection
  - Automatic retry with exponential backoff
- **Custom Hooks**: Centralized data fetching in `src/hooks/` with naming pattern `use[Entity]QueryV2.ts`

#### Authentication & Authorization
- **Auth Context**: `src/contexts/AuthContext.tsx` manages authentication state
- **Route Protection**: `src/components/auth/ProtectedRoute.tsx` handles access control
- **Session Management**: `src/components/auth/SessionManager.tsx` with 72-hour idle timeout
- **Permissions**: Role-based access control (Admin, Property Manager, Service Provider)

#### Component Architecture
- **Layout**: `src/components/layout/MainLayout.tsx` with vertical sidebar navigation
- **Feature Modules**: Organized by domain (properties, inventory, maintenance, etc.)
- **UI Components**: Reusable components in `src/components/ui/` using shadcn/ui patterns
- **Error Handling**: Global error boundary in `src/providers/AppProviders.tsx`

#### PWA Features
- **Service Worker**: Registered in `main.tsx` for offline support
- **Install Prompt**: `src/components/pwa/InstallPWAPrompt.tsx`
- **Offline Fallback**: `public/offline.html`
- **Manifest**: `public/manifest.json` with app icons

### Key Directories

- `src/pages/` - Route components and main page views
- `src/components/` - Reusable UI components organized by feature
- `src/hooks/` - Custom React hooks for data fetching and state management
- `src/contexts/` - React context providers for global state
- `src/integrations/supabase/` - Supabase client configuration and types
- `src/utils/` - Utility functions and helpers
- `supabase/` - Database migrations, functions, and configuration

### Data Flow Patterns

#### Query Management
- Use `useStandardQuery` hook for consistent caching and error handling
- Query keys follow pattern: `[entity, userId, teamId, ...params]`
- Mutations invalidate related queries automatically
- Global refresh context manages cross-component data updates

#### State Management
- Server state managed by TanStack Query
- Local UI state managed by React hooks
- Global app state in React contexts (Auth, Onboarding, etc.)
- Form state handled by React Hook Form with Zod validation

### Development Guidelines

#### File Organization
- Components use PascalCase naming
- Hooks use camelCase with `use` prefix
- Types defined in `src/types/` or co-located with components
- API calls centralized in custom hooks

#### Testing Strategy
- Unit tests for components and hooks
- Integration tests for user flows
- Test files use `.test.tsx` or `.test.ts` extension
- Mock service worker configured for API testing

#### Environment Configuration
- Development: Uses local Supabase or remote with dev config (full local supabase CLI and remote API access)
- Production: Uses production Supabase instance
- Environment variables prefixed with `VITE_`

### Chrome Extension Integration
- Extension files in `chrome/` directory
- API routes in `src/api/` for extension communication
- Middleware configured in `vite.config.ts` for development server
- Token-based authentication for extension API calls

### Multi-tenancy
- Team-based data isolation using Row Level Security (RLS)
- Team membership managed through `team_members` table
- Property access controlled by team associations
- Service provider permissions for cross-team maintenance access