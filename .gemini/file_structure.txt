.
./.vscode
./.vscode/extensions.json
./.vscode/settings.json
./supabase
./supabase/functions
./supabase/functions/_shared
./supabase/functions/_shared/storage-setup.ts
./supabase/functions/_shared/ensure-buckets.ts
./supabase/functions/_shared/cors.ts
./supabase/functions/admin-backup-database
./supabase/functions/admin-backup-database/index.ts
./supabase/functions/admin-get-all-users
./supabase/functions/admin-get-all-users/index.ts
./supabase/functions/admin-get-upload-url
./supabase/functions/admin-get-upload-url/index.ts
./supabase/functions/admin-get-users
./supabase/functions/admin-get-users/index.ts
./supabase/functions/admin-restore-database
./supabase/functions/admin-restore-database/index.ts
./supabase/functions/ai-command-processor
./supabase/functions/ai-command-processor/index.ts
./supabase/functions/ai-maintenance-items
./supabase/functions/ai-maintenance-items/index.ts
./supabase/functions/config.toml
./supabase/functions/create-team-invitation
./supabase/functions/create-team-invitation/config.toml
./supabase/functions/create-team-invitation/index.ts
./supabase/functions/fetch-ical-data
./supabase/functions/fetch-ical-data/index.ts
./supabase/functions/get-user-by-id
./supabase/functions/get-user-by-id/index.ts
./supabase/functions/maintenance-response
./supabase/functions/maintenance-response/index.ts
./supabase/functions/scrape-product
./supabase/functions/scrape-product/index.ts
./supabase/functions/send-email
./supabase/functions/send-email/.env
./supabase/functions/send-email/index.ts
./supabase/functions/sync-property-calendars
./supabase/functions/sync-property-calendars/index.ts
./supabase/functions/tsconfig.json
./supabase/functions/types.d.ts
./supabase/functions/upload-damage-photo
./supabase/functions/upload-damage-photo/index.ts
./supabase/functions/upload-invoice-pdf
./supabase/functions/upload-invoice-pdf/index.ts
./supabase/functions/upload-property-image
./supabase/functions/upload-property-image/index.ts
./supabase/functions/generate-extension-token
./supabase/functions/generate-extension-token/index.ts
./supabase/functions/upload-inventory-image
./supabase/functions/upload-inventory-image/index.ts
./supabase/functions/register-service-provider
./supabase/functions/register-service-provider/index.ts
./supabase/functions/verify-maintenance-token
./supabase/functions/verify-maintenance-token/index.ts
./supabase/functions/create-storage-buckets
./supabase/functions/create-storage-buckets/index.ts
./supabase/functions/manage-user
./supabase/functions/manage-user/index.ts
./supabase/functions/execute-sql
./supabase/functions/execute-sql/index.ts
./supabase/functions/admin-delete-user
./supabase/functions/admin-delete-user/index.ts
./supabase/functions/admin-force-delete-user
./supabase/functions/admin-force-delete-user/index.ts
./supabase/functions/fix-missing-profiles
./supabase/functions/fix-missing-profiles/index.ts
./supabase/functions/deno.json
./supabase/functions/get-maintenance-tasks
./supabase/functions/get-maintenance-tasks/deno.json
./supabase/functions/get-maintenance-tasks/index.ts
./supabase/functions/get-team-maintenance-tasks
./supabase/functions/get-team-maintenance-tasks/deno.json
./supabase/functions/get-team-maintenance-tasks/index.ts
./supabase/functions/get-property-manager-teams
./supabase/functions/get-property-manager-teams/deno.json
./supabase/functions/get-property-manager-teams/index.ts
./supabase/functions/accept-invitation-direct
./supabase/functions/accept-invitation-direct/index.ts
./supabase/functions/accept-invitation
./supabase/functions/accept-invitation/index.ts
./supabase/functions/confirm-user-email
./supabase/functions/confirm-user-email/index.ts
./supabase/functions/deno.lock
./supabase/functions/process-automation-queue
./supabase/functions/process-automation-queue/index.ts
./supabase/functions/process-booking-automation
./supabase/functions/process-booking-automation/index.ts
./supabase/functions/scheduled-automation-processor
./supabase/functions/scheduled-automation-processor/index.ts
./supabase/functions/trigger-automation-processing
./supabase/functions/trigger-automation-processing/index.ts
./supabase/migrations
./supabase/migrations/20250704000001_remote_schema.sql
./supabase/sql
./supabase/sql/create_backup_functions.sql
./supabase/sql/create_invoice_storage_policy.sql
./supabase/sql/create_pg_get_tabledef_function.sql
./supabase/.gitignore
./supabase/.branches
./supabase/.branches/_current_branch
./supabase/backups
./supabase/backups/migrations
./supabase/backups/migrations/20250510000001_sync_service_providers_across_teams.sql
./supabase/backups/roles.sql
./supabase/backups/schema.sql
./supabase/backups/data.sql
./supabase/migrations_backup
./supabase/migrations_backup/20240101000000_create_storage_buckets.sql
./supabase/migrations_backup/20240102000000_add_storage_policies.sql
./supabase/migrations_backup/20240103000000_create_storage_buckets_with_policies.sql
./supabase/migrations_backup/20240403000000_create_permission_type_enum.sql
./supabase/migrations_backup/20240404000000_create_storage_bucket.sql
./supabase/migrations_backup/20240404000002_update_permission_type.sql
./supabase/migrations_backup/20240501000000_add_teams_rls_policies.sql
./supabase/migrations_backup/20240501000001_add_team_invitation_functions.sql
./supabase/migrations_backup/20240501000002_add_teams_rls_policies_only.sql
./supabase/migrations_backup/20240501000003_add_team_invitation_function.sql
./supabase/migrations_backup/20240501000004_add_new_user_trigger.sql
./supabase/migrations_backup/20240501000005_fix_storage_policies.sql
./supabase/migrations_backup/20240501000006_teams_rls_only.sql
./supabase/migrations_backup/20240520000000_add_delete_property_cascade_function.sql
./supabase/migrations_backup/20240520000001_add_execute_sql_query_function.sql
./supabase/migrations_backup/20240601000000_create_property_documents.sql
./supabase/migrations_backup/20240701000000_add_team_properties.sql
./supabase/migrations_backup/20240701000001_add_execute_sql_query_function.sql
./supabase/migrations_backup/20250413172013_add_manage_staff_permission.sql
./supabase/migrations_backup/20250413172211_fix_storage_policies_table.sql
./supabase/migrations_backup/20250414170837_init_storage.sql
./supabase/migrations_backup/20250415000000_add_team_id_to_inventory_items.sql
./supabase/migrations_backup/20250415000001_update_rls_for_team_data_sync.sql
./supabase/migrations_backup/20250415000002_update_purchase_order_items_rls.sql
./supabase/migrations_backup/20250415000003_sync_existing_inventory_team_ids.sql
./supabase/migrations_backup/20250415000004_fix_maintenance_task_permissions.sql
./supabase/migrations_backup/20250415000005_service_provider_permissions.sql
./supabase/migrations_backup/20250415000006_service_provider_view_permissions.sql
./supabase/migrations_backup/20250415000007_service_provider_update_permissions.sql
./supabase/migrations_backup/20250415000008_service_provider_view_properties.sql
./supabase/migrations_backup/20250415000009_service_provider_update_own_tasks.sql
./supabase/migrations_backup/20250415000010_property_manager_view_team_tasks.sql
./supabase/migrations_backup/20250415000011_property_manager_view_property_tasks.sql
./supabase/migrations_backup/20250415000012_is_service_provider_in_team_function.sql
./supabase/migrations_backup/20250415000013_property_manager_view_team_service_provider_tasks.sql
./supabase/migrations_backup/20250416000001_clarify_super_admin_property_access.sql
./supabase/migrations_backup/20250416000002_fix_user_preferences_rls.sql
./supabase/migrations_backup/20250416000003_fix_maintenance_tasks_impersonation.sql
./supabase/migrations_backup/20250416000003_fix_user_preferences_rls_with_check.sql
./supabase/migrations_backup/20250416000004_create_user_preferences_rpc.sql
./supabase/migrations_backup/20250417_improve_user_creation.sql
./supabase/migrations_backup/20250418000000_fix_team_properties_recursion.sql
./supabase/migrations_backup/20250418100000_fix_team_properties_rls_completely.sql
./supabase/migrations_backup/20250418100001_add_remove_property_from_team_function.sql
./supabase/migrations_backup/20250418110000_create_team_properties_procedures.sql
./supabase/migrations_backup/20250418110001_fix_get_team_properties.sql
./supabase/migrations_backup/20250418120000_convert_procedures_to_functions.sql
./supabase/migrations_backup/20250418120001_create_direct_sql_function.sql
./supabase/migrations_backup/20250418120002_create_remove_property_direct.sql
./supabase/migrations_backup/20250418130000_fix_team_invitation_issue.sql
./supabase/migrations_backup/20250418130001_fix_invitation_acceptance_function.sql
./supabase/migrations_backup/20250418140000_fix_team_access_policies.sql
./supabase/migrations_backup/20250418140001_fix_team_members_view.sql
./supabase/migrations_backup/20250418150000_add_property_team_access_function.sql
./supabase/migrations_backup/20250418160000_fix_property_team_access.sql
./supabase/migrations_backup/20250418170000_update_property_rls_policies.sql
./supabase/migrations_backup/20250418180000_fix_team_property_access.sql
./supabase/migrations_backup/20250418190000_fix_team_properties_rls.sql
./supabase/migrations_backup/20250418200000_fix_team_members_rls.sql
./supabase/migrations_backup/20250418210000_fix_teams_rls.sql
./supabase/migrations_backup/20250418220000_add_team_property_access_trigger.sql
./supabase/migrations_backup/20250418230000_add_user_permission_functions.sql
./supabase/migrations_backup/20250418240000_add_user_properties_function.sql
./supabase/migrations_backup/20250418250000_fix_user_permissions_rls.sql
./supabase/migrations_backup/20250418260000_fix_team_members_recursion.sql
./supabase/migrations_backup/20250418270000_fix_team_properties_recursion.sql
./supabase/migrations_backup/20250418280000_fix_user_permissions_recursion.sql
./supabase/migrations_backup/20250418290000_fix_teams_recursion.sql
./supabase/migrations_backup/20250418300000_fix_properties_recursion.sql
./supabase/migrations_backup/20250418310000_drop_all_team_members_policies.sql
./supabase/migrations_backup/20250418320000_drop_all_team_members_policies.sql
./supabase/migrations_backup/20250418340000_fix_maintenance_tasks_policies.sql
./supabase/migrations_backup/20250418350000_add_team_member_maintenance_policy.sql
./supabase/migrations_backup/20250418360000_simplify_properties_policies.sql
./supabase/migrations_backup/20250418370000_simplify_inventory_items_policies.sql
./supabase/migrations_backup/20250418380000_simplify_team_properties_policies.sql
./supabase/migrations_backup/20250418390000_simplify_user_permissions_policies.sql
./supabase/migrations_backup/20250418400000_simplify_teams_policies.sql
./supabase/migrations_backup/20250418410000_simplify_maintenance_tasks_policies.sql
./supabase/migrations_backup/20250418420000_add_simple_property_function.sql
./supabase/migrations_backup/20250418430000_fix_team_properties_duplicates.sql
./supabase/migrations_backup/20250418450000_remove_duplicate_team_properties.sql
./supabase/migrations_backup/20250418460000_add_unique_constraint_team_properties.sql
./supabase/migrations_backup/20250420000000_update_maintenance_tasks_team_id.sql
./supabase/migrations_backup/20250420000001_update_maintenance_tasks_for_molly_team.sql
./supabase/migrations_backup/20250420000003_update_maintenance_tasks_team_id_for_all_teams.sql
./supabase/migrations_backup/20250420000005_add_maintenance_tasks_property_index.sql
./supabase/migrations_backup/20250420000006_fix_maintenance_tasks_rpc.sql
./supabase/migrations_backup/20250420000007_fix_maintenance_tasks_direct_query.sql
./supabase/migrations_backup/20250421184114_fix_team_inventory_sync.sql
./supabase/migrations_backup/20250422050421_fix_duplicate_damage_reports.sql
./supabase/migrations_backup/20250422051136_fix_damage_reports_display.sql
./supabase/migrations_backup/20250422051450_fix_damage_reports_type.sql
./supabase/migrations_backup/20250422051729_fix_damage_reports_ambiguous_column.sql
./supabase/migrations_backup/20250422051836_fix_damage_reports_duplicates.sql
./supabase/migrations_backup/20250422052158_fix_remaining_ambiguous_columns.sql
./supabase/migrations_backup/20250422052745_fix_damage_reports_delete_policy.sql
./supabase/migrations_backup/20250422052832_fix_damage_reports_duplicates_trigger.sql
./supabase/migrations_backup/20250422052918_fix_damage_reports_team_sync.sql
./supabase/migrations_backup/20250422053140_fix_damage_reports_ambiguous_team_id.sql
./supabase/migrations_backup/20250422053312_create_simple_damage_reports_function.sql
./supabase/migrations_backup/20250422053502_fix_damage_reports_ambiguous_id.sql
./supabase/migrations_backup/20250422054029_fix_duplicate_archerfield_properties.sql
./supabase/migrations_backup/20250422060926_fix_damage_reports_property_name.sql
./supabase/migrations_backup/20250422061106_fix_duplicate_properties.sql
./supabase/migrations_backup/20250422061816_create_get_user_properties_function.sql
./supabase/migrations_backup/20250422062305_add_team_id_to_properties.sql
./supabase/migrations_backup/20250422062927_fix_damage_report_pdf_and_invoices.sql
./supabase/migrations_backup/20250422063721_fix_purchase_order_properties.sql
./supabase/migrations_backup/20250423000000_add_user_to_team_function.sql
./supabase/migrations_backup/20250423000001_add_service_provider_permissions_function.sql
./supabase/migrations_backup/20250423000002_create_accept_invitation_function.sql
./supabase/migrations_backup/20250425000001_add_get_service_provider_properties.sql
./supabase/migrations_backup/20250429000001_update_property_access_functions.sql
./supabase/migrations_backup/20250501000007_fix_invoice_items_rls.sql
./supabase/migrations_backup/20250506_create_get_user_inventory_items.sql
./supabase/migrations_backup/20250510000001_sync_service_providers_across_teams.sql
./supabase/migrations_backup/20250704000000_restore_from_backup.sql
./supabase/migrations_backup/20250704000001_basic_setup.sql
./supabase/config.toml.backup
./supabase/seed.sql.backup
./supabase/config.toml
./supabase/.temp
./supabase/.temp/cli-latest
./supabase/.temp/postgres-version
./supabase/.temp/pooler-url
./supabase/.temp/gotrue-version
./supabase/.temp/rest-version
./supabase/.temp/storage-version
./supabase/.temp/project-ref
./cypress
./cypress/e2e
./cypress/e2e/auth
./cypress/e2e/auth/login.cy.ts
./cypress/e2e/auth/register.cy.ts
./cypress/e2e/properties
./cypress/e2e/properties/property-management.cy.ts
./cypress/support
./cypress/support/commands.ts
./cypress/support/e2e.ts
./cypress/tsconfig.json
./cypress/screenshots
./cypress/screenshots/auth
./cypress/screenshots/auth/login.cy.ts
./cypress/screenshots/auth/login.cy.ts/Login Page -- should display login form with all fields -- before each hook (failed).png
./cypress/screenshots/auth/register.cy.ts
./cypress/screenshots/auth/register.cy.ts/Registration Page -- should display registration form with all fields -- before each hook (failed).png
./cypress/screenshots/properties
./cypress/screenshots/properties/property-management.cy.ts
./cypress/screenshots/properties/property-management.cy.ts/Property Management -- should display property list -- before each hook (failed).png
./public
./public/favicon.ico
./public/og-image.png
./public/placeholder.svg
./public/icons
./public/icons/apple-touch-icon.png
./public/icons/favicon.ico
./public/icons/icon-512x512.png
./public/icons/icon-192x192.png
./public/icons/icon-32x32.png
./public/icons/icon-16x16.png
./public/icons/logo.png
./public/offline.html
./public/amazon-icon.svg
./public/walmart-icon.svg
./public/invite-redirect.html
./public/manifest.json
./public/service-worker.js
./src
./src/components
./src/components/admin
./src/components/admin/SettingsTab.tsx
./src/components/admin/AdminErrorState.tsx
./src/components/admin/DatabaseTab.tsx
./src/components/admin/AdminCard.test.tsx
./src/components/admin/AdminCard.tsx
./src/components/admin/AdminTable.test.tsx
./src/components/admin/AdminTable.tsx
./src/components/admin/UserList.tsx
./src/components/admin/UserManagement.tsx
./src/components/admin/UsersTab.tsx
./src/components/admin/hooks
./src/components/admin/hooks/useAdminActions.ts
./src/components/admin/hooks/useRefreshLogic.ts
./src/components/admin/hooks/useFileUpload.ts
./src/components/admin/hooks/useSupabaseSession.ts
./src/components/admin/hooks/index.ts
./src/components/admin/utils
./src/components/admin/utils/index.ts
./src/components/admin/utils/userTableColumns.tsx
./src/components/admin/types
./src/components/admin/types/index.ts
./src/components/admin/dialogs
./src/components/admin/dialogs/EditUserDialog.tsx
./src/components/admin/dialogs/ResetPasswordDialog.tsx
./src/components/admin/dialogs/DeleteUserDialog.tsx
./src/components/admin/dialogs/ForceDeleteUserDialog.tsx
./src/components/admin/database
./src/components/admin/database/BackupSection.tsx
./src/components/admin/database/RestoreSection.tsx
./src/components/admin/database/RestorePreviewDialog.tsx
./src/components/auth
./src/components/auth/ProtectedRoute.test.tsx
./src/components/auth/PermissionGate.tsx
./src/components/auth/IdleTimeoutManager.tsx
./src/components/auth/SessionRefreshManager.tsx
./src/components/auth/SessionManager.tsx
./src/components/auth/RequireAuth.tsx
./src/components/auth/ProtectedRoute.tsx
./src/components/auth/hooks
./src/components/auth/hooks/useSessionValidation.ts
./src/components/auth/hooks/useSessionStorage.ts
./src/components/auth/hooks/useTimerCleanup.ts
./src/components/auth/hooks/useNetworkStatus.ts
./src/components/auth/hooks/useAuthRedirect.ts
./src/components/auth/hooks/index.ts
./src/components/auth/utils
./src/components/auth/utils/index.ts
./src/components/auth/types
./src/components/auth/types/index.ts
./src/components/auth/constants
./src/components/auth/constants/index.ts
./src/components/damages
./src/components/damages/detail-tabs
./src/components/damages/detail-tabs/invoice
./src/components/damages/detail-tabs/invoice/api
./src/components/damages/detail-tabs/invoice/api/invoiceApi.ts
./src/components/damages/detail-tabs/invoice/utils
./src/components/damages/detail-tabs/invoice/utils/pdfGenerator.ts
./src/components/damages/detail-tabs/invoice/InvoiceHeader.tsx
./src/components/damages/detail-tabs/invoice/useInvoiceManager.tsx
./src/components/damages/detail-tabs/invoice/InvoiceList.tsx
./src/components/damages/detail-tabs/photos
./src/components/damages/detail-tabs/photos/PhotosHeader.tsx
./src/components/damages/detail-tabs/photos/PhotoUploadDialog.tsx
./src/components/damages/detail-tabs/photos/PhotosTab.tsx
./src/components/damages/detail-tabs/ActivityTab.tsx
./src/components/damages/detail-tabs/PDFExportOptions.tsx
./src/components/damages/detail-tabs/GenerateReportsTab.tsx
./src/components/damages/detail-tabs/InvoiceTab.tsx
./src/components/damages/detail-tabs/NotesTab.tsx
./src/components/damages/detail-tabs/OverviewTab.tsx
./src/components/damages/detail
./src/components/damages/detail/DamageDetailSkeleton.tsx
./src/components/damages/detail/DamageDetailHeader.tsx
./src/components/damages/detail/DamageNotFound.tsx
./src/components/damages/invoice
./src/components/damages/invoice/FileUploadArea.tsx
./src/components/damages/invoice/InvoiceDetailsForm.tsx
./src/components/damages/invoice/InvoiceFormFields.tsx
./src/components/damages/invoice/UploadInvoiceDialog.tsx
./src/components/damages/invoice/CreateInvoiceDialog.tsx
./src/components/damages/invoice/InvoiceItemManager.tsx
./src/components/damages/invoice/useInvoiceForm.ts
./src/components/damages/AddDamageReport.tsx
./src/components/damages/DamageReportPdfExport.tsx
./src/components/damages/InvoiceDetailsDialog.tsx
./src/components/damages/EditDamageReport.tsx
./src/components/damages/DamageReportDetailDialog.tsx
./src/components/damages/AddDamageReportDialog.tsx
./src/components/damages/GenerateDamageReportPdf.ts
./src/components/damages/DamageReportCard.tsx
./src/components/damages/types
./src/components/damages/types/index.ts
./src/components/damages/constants
./src/components/damages/constants/index.ts
./src/components/damages/utils
./src/components/damages/utils/index.ts
./src/components/damages/hooks
./src/components/damages/hooks/useDamageReportData.ts
./src/components/damages/hooks/useDamageReportMutations.ts
./src/components/damages/hooks/usePhotoOperations.ts
./src/components/damages/hooks/useLoadingState.ts
./src/components/damages/hooks/index.ts
./src/components/damages/components
./src/components/damages/components/StatusSelect.tsx
./src/components/damages/components/SeveritySelect.tsx
./src/components/damages/components/PlatformSelect.tsx
./src/components/damages/components/LoadingSpinner.tsx
./src/components/damages/components/DeleteConfirmDialog.tsx
./src/components/damages/components/CurrencyInput.tsx
./src/components/damages/components/index.ts
./src/components/dashboard
./src/components/dashboard/AiCommandCenter.tsx
./src/components/dashboard/DashboardView.tsx
./src/components/inventory
./src/components/inventory/InventoryList.tsx
./src/components/inventory/ChromeExtensionStatus.tsx
./src/components/inventory/AmazonProductCard.tsx
./src/components/inventory/types.ts
./src/components/inventory/InventoryImageUploader.tsx
./src/components/inventory/ForceCloseDialog.tsx
./src/components/inventory/InventoryCard.tsx
./src/components/inventory/InventoryDialog.test.tsx
./src/components/inventory/InventoryFilterBar.tsx
./src/components/inventory/InventoryGrid.tsx
./src/components/inventory/InventoryTable.tsx
./src/components/inventory/PurchaseOrderDetailsDialog.tsx
./src/components/inventory/InventoryContent.test.tsx
./src/components/inventory/InventoryDialog.tsx
./src/components/inventory/InventoryContent.tsx
./src/components/inventory/BulkImportDialog.tsx
./src/components/inventory/AmazonSearch.tsx
./src/components/inventory/CreatePurchaseOrderDialog.tsx
./src/components/inventory/InventoryHeader.tsx
./src/components/inventory/InventoryImage.tsx
./src/components/inventory/InventorySummary.tsx
./src/components/inventory/AdvancedSearch.tsx
./src/components/inventory/ExportInventory.tsx
./src/components/inventory/InventoryAnalytics.tsx
./src/components/layout
./src/components/layout/PageTransition.tsx
./src/components/layout/Navbar.tsx
./src/components/layout/MainLayout.tsx
./src/components/layout/PermissionBasedNavigation.tsx
./src/components/layout/VerticalSidebar.tsx
./src/components/maintenance
./src/components/maintenance/forms
./src/components/maintenance/forms/MaintenanceTaskForm.tsx
./src/components/maintenance/hooks
./src/components/maintenance/hooks/useProviderForm.tsx
./src/components/maintenance/hooks/useMaintenanceTaskForm.ts
./src/components/maintenance/utils
./src/components/maintenance/utils/providerValidation.ts
./src/components/maintenance/MaintenanceTaskForm.tsx
./src/components/maintenance/ProviderDialog.tsx
./src/components/maintenance/ProviderFormField.tsx
./src/components/maintenance/ProviderManagement.tsx
./src/components/maintenance/ProviderForm.tsx
./src/components/maintenance/ProviderList.tsx
./src/components/maintenance/MaintenanceCard.tsx
./src/components/maintenance/AddMaintenanceDialog.tsx
./src/components/maintenance/AiMaintenanceDialog.tsx
./src/components/maintenance/MaintenanceDetailsDialog.tsx
./src/components/maintenance/MaintenanceResponseHandler.tsx
./src/components/maintenance/MaintenanceTabs.tsx
./src/components/maintenance/PrintDialog.tsx
./src/components/maintenance/PrintableMaintenanceList.tsx
./src/components/maintenance/types.ts
./src/components/maintenance/MaintenanceList.tsx
./src/components/onboarding
./src/components/onboarding/DashboardWizard.tsx
./src/components/onboarding/FeatureTour.tsx
./src/components/properties
./src/components/properties/calendar
./src/components/properties/calendar/CalendarFilters.tsx
./src/components/properties/calendar/EventBadge.tsx
./src/components/properties/calendar/EventList.tsx
./src/components/properties/calendar/calendarUtils.ts
./src/components/properties/calendar/icalParser.ts
./src/components/properties/calendar/index.tsx
./src/components/properties/calendar/types.ts
./src/components/properties/calendar/eventHelpers.ts
./src/components/properties/calendar/BookingCalendar.tsx
./src/components/properties/calendar/useCalendarEvents.ts
./src/components/properties/calendar/CalendarLegend.tsx
./src/components/properties/calendar/CalendarTest.tsx
./src/components/properties/AddProperty.tsx
./src/components/properties/CollectionManager.tsx
./src/components/properties/CollectionsTab.tsx
./src/components/properties/EditProperty.tsx
./src/components/properties/PropertyActions.tsx
./src/components/properties/PropertyCard.test.tsx
./src/components/properties/PropertyCollectionsManager.tsx
./src/components/properties/PropertyDetailHeader.tsx
./src/components/properties/PropertyFilters.tsx
./src/components/properties/PropertyOverviewTab.tsx
./src/components/properties/PropertyPendingItems.tsx
./src/components/properties/PropertySummary.tsx
./src/components/properties/PropertyDetailsDialog.tsx
./src/components/properties/ImageUploader.tsx
./src/components/properties/PropertyCalendarSync.tsx
./src/components/properties/PropertyHeader.tsx
./src/components/properties/PropertyEditForm.tsx
./src/components/properties/AddPropertyDialog.tsx
./src/components/properties/PropertyCard.tsx
./src/components/properties/PropertyDocumentsTab.tsx
./src/components/properties/PropertyGrid.tsx
./src/components/properties/PropertyList.tsx
./src/components/properties/documents
./src/components/properties/documents/AddDocumentDialog.tsx
./src/components/properties/documents/AddFileDialog.tsx
./src/components/properties/documents/EditFileDialog.tsx
./src/components/properties/documents/FilePreviewDialog.tsx
./src/components/properties/documents/PropertyDocumentsList.tsx
./src/components/properties/documents/PropertyFilesList.tsx
./src/components/properties/documents/ViewDocumentDialog.tsx
./src/components/properties/PropertyListView.tsx
./src/components/settings
./src/components/settings/DefaultContent.tsx
./src/components/settings/HelpSettings.tsx
./src/components/settings/NotificationSettings.tsx
./src/components/settings/SettingsHeader.tsx
./src/components/settings/ToggleOption.tsx
./src/components/settings/ApiIntegrations.tsx
./src/components/settings/ApiTokenManager.tsx
./src/components/settings/SecuritySettings.tsx
./src/components/settings/ProfileSettings.tsx
./src/components/settings/BackupSettings.tsx
./src/components/settings/AppearanceSettings.tsx
./src/components/settings/SettingsSidebar.tsx
./src/components/settings/AccountSettings.tsx
./src/components/teams
./src/components/teams/TeamSettings.tsx
./src/components/teams/InviteTeamMemberDialog.tsx
./src/components/teams/TeamManagement.tsx
./src/components/teams/SentInvitationsDisplay.tsx
./src/components/teams/TeamPropertiesManager.tsx
./src/components/teams/PermissionManagement.tsx
./src/components/teams/TeamMemberManagement.tsx
./src/components/teams/hooks
./src/components/teams/hooks/useTeamData.ts
./src/components/teams/types
./src/components/teams/types/types.ts
./src/components/teams/index.ts
./src/components/theme
./src/components/theme/ThemeInitializer.tsx
./src/components/ui
./src/components/ui/GlassCard.tsx
./src/components/ui/Tooltip.tsx
./src/components/ui/Wizard.tsx
./src/components/ui/accordion.tsx
./src/components/ui/address-autocomplete.tsx
./src/components/ui/alert-dialog.tsx
./src/components/ui/alert.tsx
./src/components/ui/aspect-ratio.tsx
./src/components/ui/avatar.tsx
./src/components/ui/badge.tsx
./src/components/ui/breadcrumb.tsx
./src/components/ui/card.tsx
./src/components/ui/carousel.tsx
./src/components/ui/chart.tsx
./src/components/ui/checkbox.tsx
./src/components/ui/circle-progress.tsx
./src/components/ui/collapsible.tsx
./src/components/ui/command.tsx
./src/components/ui/context-menu.tsx
./src/components/ui/drawer.tsx
./src/components/ui/dropdown-menu.tsx
./src/components/ui/form.tsx
./src/components/ui/hover-card.tsx
./src/components/ui/input-otp.tsx
./src/components/ui/input.tsx
./src/components/ui/label.tsx
./src/components/ui/menubar.tsx
./src/components/ui/navigation-menu.tsx
./src/components/ui/pagination.tsx
./src/components/ui/popover.tsx
./src/components/ui/progress.tsx
./src/components/ui/radio-group.tsx
./src/components/ui/resizable.tsx
./src/components/ui/scroll-area.tsx
./src/components/ui/select.tsx
./src/components/ui/separator.tsx
./src/components/ui/sheet.tsx
./src/components/ui/sidebar.tsx
./src/components/ui/skeleton.tsx
./src/components/ui/slider.tsx
./src/components/ui/table.tsx
./src/components/ui/tabs.tsx
./src/components/ui/textarea.tsx
./src/components/ui/toast.tsx
./src/components/ui/toaster.tsx
./src/components/ui/toggle-group.tsx
./src/components/ui/toggle.tsx
./src/components/ui/use-toast.ts
./src/components/ui/button.tsx
./src/components/ui/switch.tsx
./src/components/ui/dialog.tsx
./src/components/ui/shadcn-tooltip.tsx
./src/components/ui/sonner.tsx
./src/components/ui/EmptyState.tsx
./src/components/ui/ErrorState.tsx
./src/components/ui/LoadingState.tsx
./src/components/ui/calendar.tsx
./src/components/ui/ButtonStandardization.tsx
./src/components/ui/FilterStandardization.tsx
./src/components/ui/ConnectionStatusIndicator.tsx
./src/components/ui/StandardizedUI.tsx
./src/components/ui/StatCard.tsx
./src/components/pwa
./src/components/pwa/InstallPWAPrompt.tsx
./src/components/common
./src/components/common/PermissionGuard.tsx
./src/components/common/SimplePagination.tsx
./src/components/common/RefreshButton.tsx
./src/components/common/types
./src/components/common/types/index.ts
./src/components/common/constants
./src/components/common/constants/index.ts
./src/components/common/utils
./src/components/common/utils/index.ts
./src/components/common/hooks
./src/components/common/hooks/usePagination.ts
./src/components/common/hooks/usePermissionCheck.ts
./src/components/common/hooks/useRefreshStrategies.ts
./src/components/common/hooks/index.ts
./src/components/common/components
./src/components/common/components/PaginationItem.tsx
./src/components/common/components/PaginationNavigation.tsx
./src/components/common/components/index.ts
./src/components/automation
./src/components/automation/AutomationRules.tsx
./src/components/automation/types
./src/components/automation/types/index.ts
./src/components/automation/constants
./src/components/automation/constants/index.ts
./src/components/automation/utils
./src/components/automation/utils/index.ts
./src/components/automation/hooks
./src/components/automation/hooks/useAutomationForm.ts
./src/components/automation/hooks/useAutomationDialog.ts
./src/components/automation/hooks/useAutomationActions.ts
./src/components/automation/hooks/index.ts
./src/components/automation/components
./src/components/automation/components/AutomationRuleCard.tsx
./src/components/automation/components/PropertySelector.tsx
./src/components/automation/components/AutomationRuleForm.tsx
./src/components/automation/components/AutomationRuleDialog.tsx
./src/components/automation/components/AutomationRulesList.tsx
./src/components/automation/components/AutomationHeader.tsx
./src/components/automation/components/index.ts
./src/components/debug
./src/components/debug/PropertyDebugger.tsx
./src/components/debug/DataLoadingDebugger.tsx
./src/components/debug/DataPreloaderDebugger.tsx
./src/components/debug/RenderErrorBoundary.tsx
./src/components/debug/PermissionsDebug.tsx
./src/components/debug/DataLoadingTestComponent.tsx
./src/contexts
./src/contexts/OnboardingContext.tsx
./src/contexts/ImpersonationContext.tsx
./src/contexts/AuthContextSimple.tsx
./src/contexts/GlobalDataRefreshContext.tsx
./src/contexts/AuthContext.tsx
./src/hooks
./src/hooks/use-media-query.ts
./src/hooks/use-mobile.tsx
./src/hooks/useDisclosure.ts
./src/hooks/usePendingItems.tsx
./src/hooks/useProperties.test.tsx
./src/hooks/useDialog.ts
./src/hooks/useImageUpload.ts
./src/hooks/useInventoryFilters.ts
./src/hooks/useInventoryFilters.tsx
./src/hooks/usePropertyActions.tsx
./src/hooks/usePropertyFilters.tsx
./src/hooks/useDataLoader.ts
./src/hooks/useMaintenanceTasks.tsx
./src/hooks/usePropertyUpdate.ts
./src/hooks/useSessionData.ts
./src/hooks/useThemeSettings.ts
./src/hooks/useAdmin.test.tsx
./src/hooks/useAdmin.ts
./src/hooks/useDamageDetail.tsx
./src/hooks/useInventoryOperations.ts
./src/hooks/useMaintenanceTasksRPC.tsx
./src/hooks/useOperationsData.ts
./src/hooks/use-toast.ts
./src/hooks/usePermissionManagement.ts
./src/hooks/useProperties.tsx
./src/hooks/usePropertyDetail.ts
./src/hooks/useTeamManagement.ts
./src/hooks/useVisibilityRefresh.ts
./src/hooks/useAppearanceSettingsQuery.ts
./src/hooks/useAppearanceSettingsQueryV2.ts
./src/hooks/useDamageReportsQueryV2.ts
./src/hooks/useDataPreloader.ts
./src/hooks/usePropertiesRebuild.ts
./src/hooks/usePropertiesSimple.ts
./src/hooks/useQueryInitialization.ts
./src/hooks/useStandardQuery.ts
./src/hooks/useTeamManagementQueryV2.ts
./src/hooks/useInventoryQueryV2.ts
./src/hooks/useMaintenanceTasksQueryV2.ts
./src/hooks/useOperationsDataQuery.ts
./src/hooks/usePropertiesQueryV2.ts
./src/hooks/useTaskAutomationQueryV2.ts
./src/hooks/useNavigationRefresh.ts
./src/hooks/usePurchaseOrders.ts
./src/hooks/useDamageReports.tsx
./src/hooks/useDashboardData.tsx
./src/hooks/useDashboardDataFixed.tsx
./src/hooks/useDashboardDataQuery.ts
./src/hooks/usePermissions.ts
./src/hooks/usePermissionsFixed.ts
./src/hooks/useProviders.tsx
./src/hooks/usePropertyStatistics.ts
./src/integrations
./src/integrations/supabase
./src/integrations/supabase/types.ts
./src/integrations/supabase/client-simple.ts
./src/integrations/supabase/client.ts
./src/lib
./src/lib/utils.ts
./src/lib/supabase-types.ts
./src/pages
./src/pages/AddEntry.tsx
./src/pages/CollectionDetail.tsx
./src/pages/Collections.tsx
./src/pages/ContactDetail.tsx
./src/pages/Contacts.test.tsx
./src/pages/Dashboard.test.tsx
./src/pages/MaintenanceProviders.test.tsx
./src/pages/InvoicePage.tsx
./src/pages/MaintenanceProviders.tsx
./src/pages/DamageDetail.tsx
./src/pages/Contacts.tsx
./src/pages/InvoiceDetail.tsx
./src/pages/NotFound.tsx
./src/pages/Register.tsx
./src/pages/StyleGuideDemo.tsx
./src/pages/Auth.tsx
./src/pages/InvitationPage.debug.tsx
./src/pages/InvitationPage.tsx
./src/pages/AdminDashboard.tsx
./src/pages/ForgotPassword.tsx
./src/pages/Operations.tsx
./src/pages/PurchaseOrders.tsx
./src/pages/ResetPassword.tsx
./src/pages/Dashboard.tsx
./src/pages/Maintenance.tsx
./src/pages/Properties.tsx
./src/pages/PropertyDetail.tsx
./src/pages/Inventory.tsx
./src/pages/PropertiesRebuild.tsx
./src/pages/TestSimple.tsx
./src/pages/DataLoadingDebugPage.tsx
./src/pages/Login.tsx
./src/pages/Settings.tsx
./src/pages/TaskAutomation.tsx
./src/pages/Damages.tsx
./src/pages/Index.tsx
./src/pages/TeamDashboard.tsx
./src/pages/CalendarTestPage.tsx
./src/pages/DashboardFixed.tsx
./src/providers
./src/providers/QueryProvider.tsx
./src/providers/AppProviders.tsx
./src/services
./src/services/ExtensionApiService.ts
./src/services/settingsService.ts
./src/services/backupService.ts
./src/services/emailService.ts
./src/tests
./src/tests/contexts
./src/tests/contexts/AuthContext.test.tsx
./src/tests/data
./src/tests/data/damages.ts
./src/tests/data/maintenance.ts
./src/tests/data/properties.ts
./src/tests/mocks
./src/tests/mocks/fileMock.ts
./src/tests/mocks/handlers.ts
./src/tests/mocks/server.ts
./src/tests/mocks/styleMock.ts
./src/tests/templates
./src/tests/templates/componentTest.template.txt
./src/tests/templates/hookTest.template.txt
./src/tests/templates/integrationTest.template.txt
./src/tests/types
./src/tests/types/testing-library__jest-dom.d.ts
./src/tests/utils
./src/tests/utils/test-utils.tsx
./src/tests/testTeamMemberTasks.js
./src/tests/supabaseTest.ts
./src/tests/verifyTeamMembers.ts
./src/tests/acceptInvitationRpc.test.ts
./src/tests/setup.ts
./src/types
./src/types/deno.d.ts
./src/types/external-modules.d.ts
./src/types/jest-dom.d.ts
./src/types/settings.ts
./src/types/testing-library__jest-dom.d.ts
./src/types/auth.ts
./src/types/supabase.ts
./src/types/damages.ts
./src/types/inventory.d.ts
./src/types/inventory.ts
./src/utils
./src/utils/forceCloseDialog.ts
./src/utils/setupStorage.ts
./src/utils/formatters.ts
./src/utils/imageProcessing.ts
./src/utils/authErrorHandler.ts
./src/utils/sessionUtils.ts
./src/utils/calendarUtils.ts
./src/utils/createStorageBuckets.ts
./src/utils/debugUtils.ts
./src/utils/serviceWorkerRegistration.ts
./src/utils/cacheUtils.ts
./src/utils/dataLoadingTest.ts
./src/utils/propertyUtils.ts
./src/api
./src/api/extensionRoutes.ts
./src/api/automationRoutes.ts
./src/api/invitations.ts
./src/assets
./src/assets/apple-calendar-icon.svg
./src/vite-env.d.ts
./src/vitest.d.ts
./src/App.css
./src/docs
./src/docs/supabase.md
./src/docs/DATA_LOADING_FIXES_FINAL_IMPLEMENTATION.md
./src/docs/DATA_LOADING_FIXES_FINAL_STATUS.md
./src/docs/DATA_LOADING_FIXES_FINAL_SUMMARY.md
./src/docs/DATA_LOADING_FIXES_SUMMARY.md
./src/docs/DATA_LOADING_FOCUS_FIX_SUMMARY.md
./src/docs/DATA_PRELOADER_IMPLEMENTATION.md
./src/docs/DEBUG_INSTRUCTIONS.md
./src/docs/ai-purchase-order-enhancement.md
./src/docs/ai-purchase-order-fix.md
./src/docs/deploy-ai-command-processor.md
./src/docs/deployment-complete-summary.md
./src/docs/ai-maintenance-assignee-fix.md
./src/docs/assignee-fix-complete.md
./src/index.css
./src/main.tsx
./src/working.test.js
./src/App.tsx
./src/test
./src/test/team-functionality.test.md
./src/test/team-invitation-flow-test.md
./src/test/ai-maintenance-task-creation-fix.md
./src/test/ai-maintenance-rls-fix.md
./src/test/ai-maintenance-unified-fix.md
./src/test/test-ai-purchase-orders.js
./src/test/test-low-stock-direct.js
./src/test/verify-purchase-order-fix.js
./src/test/quick-test-deployed-fix.js
./src/test/debug-ai-extraction.js
./src/test/debug-assignee-issue.js
./src/test/debug-with-logs.js
./src/test/final-assignee-test.js
./src/test/test-dual-approach.js
./src/test/test-fixed-assignee.js
./src/test/test-maintenance-assignee.js
./babel.config.js
./components.json
./cypress.config.ts
./eslint.config.js
./tsconfig.node.json
./.env.example
./jest.setup.js
./jest.config.ts
./chrome
./chrome/old
./chrome/old/example.csv
./chrome/old/icon.png
./chrome/old/list.html
./chrome/old/shoplist.html
./chrome/old/styles.css
./chrome/old/list.js
./chrome/old/manifest.json
./chrome/old/shoplist.js
./chrome/old/content.js
./chrome/old/background.js
./chrome/example.csv
./chrome/icon.png
./chrome/list.html
./chrome/list.js
./chrome/shoplist.html
./chrome/styles.css
./chrome/settings.html
./chrome/shoplist.js
./chrome/imageProcessor.js
./chrome/dist
./chrome/dist/manifest.json
./chrome/dist/background.js
./chrome/dist/content.js
./chrome/dist/imageProcessor.js
./chrome/dist/settings.html
./chrome/dist/settings.js
./chrome/dist/styles.css
./chrome/dist/icon.png
./chrome/�1 402-779-8946�_04-03-25_0319PM.amr
./chrome/content.js
./chrome/settings.js
./chrome/background.js
./chrome/build.sh
./chrome/manifest.json
./.env.local
./dist
./dist/amazon-icon.svg
./dist/favicon.ico
./dist/icons
./dist/icons/apple-touch-icon.png
./dist/icons/favicon.ico
./dist/icons/icon-16x16.png
./dist/icons/icon-192x192.png
./dist/icons/icon-32x32.png
./dist/icons/icon-512x512.png
./dist/icons/logo.png
./dist/invite-redirect.html
./dist/manifest.json
./dist/offline.html
./dist/og-image.png
./dist/placeholder.svg
./dist/service-worker.js
./dist/walmart-icon.svg
./dist/assets
./dist/assets/index-CSCZ6VX3.css
./dist/assets/purify.es-BFmuJLeH.js
./dist/assets/index.es-COGao-36.js
./dist/assets/purify.es-BFmuJLeH.js.map
./dist/assets/index.es-COGao-36.js.map
./dist/assets/index-CyxlYYOD.js.map
./dist/assets/index-CyxlYYOD.js
./dist/index.html
./.supabase
./.supabase/docker-compose.override.yml
./docker-compose.override.yml
./bun.lockb
./tsconfig.app.json
./README.md
./scripts
./scripts/generate-test.js
./scripts/import_requests.py
./scripts/launch-browser-tools-mcp.sh
./scripts/update-react-query-config.js
./scripts/fix-retry-count-in-query-keys.js
./scripts/verify-query-configs.js
./scripts/test-environment.js
./scripts/setup-storage.js
./scripts/setup_podman_supabase.sh
./.env
./docs
./docs/ai-command-processor-improvements.md
./docs/style.md
./docs/task-automation.md
./docs/AuthenticationSystem.md
./docs/DataLoadingArchitecture.md
./docs/DataLoadingFixes-2023-05-09.md
./docs/DataLoadingFixes.md
./docs/DataLoadingImprovements.md
./docs/DataRefreshStrategy.md
./docs/KnownIssues.md
./docs/ManifestJsonIssue.md
./docs/NavigationRefreshFixes-2023-05-10.md
./docs/NetworkConnectivity.md
./docs/RoutingSystem.md
./docs/SessionManagement.md
./docs/SettingsAndTeamsFixes-2023-05-11.md
./docs/SettingsAndTeamsFixes-2023-05-12.md
./docs/StyleGuide.md
./docs/SupabaseGuide.md
./docs/chrome-extension.md
./docs/data-loading-fixes.md
./docs/debugging.md
./docs/focus-data-loading-fixes.md
./docs/image-handling.md
./docs/mcp-setup.md
./docs/property-documents.md
./docs/pwa.md
./docs/react-warnings-fixes.md
./docs/rls_policies.md
./docs/schema.md
./docs/service-worker-fixes.md
./docs/supabase.md
./docs/DataLoadingDebugger-2024-06-17.md
./docs/DataLoadingFixes-2024-06-17.md
./docs/AGGRESSIVE_REFRESH_FIX_SUMMARY.md
./docs/FINAL-DATA-LOADING-FIX.md
./docs/SECURITY_FIX_PHASE1_API_KEYS.md
./docs/QUERY_MATCHING_FIX_SUMMARY.md
./docs/PWA_RAPID_REFRESH_FIX.md
./docs/README-browser-tools.md
./docs/supabase-environment-setup.md
./docs/AUTH_TIMING_FIX_SUMMARY.md
./docs/ENHANCED_QUERY_MATCHING_SUMMARY.md
./docs/FINAL_TEST_PLAN.md
./docs/IMPLEMENTATION_COMPLETE.md
./docs/PERMISSIONS_LOADING_FIX.md
./docs/PHASE1_COMPLETE.md
./docs/PHASE1_ISSUE2_COMPLETE.md
./docs/TEAMS_ROUTE_FIX_SUMMARY.md
./tests
./tests/team-invitation-flow.test.js
./tests/invitation-test-final.html
./tests/invitation-test-hash.html
./tests/invitation-test.html
./tests/test-ai-command.js
./tests/test-data-loading.js
./tests/test-invitation.html
./tests/test-invitation.js
./tests/focus-test-page.html
./tests/initialize-invitation.js
./tests/listing-49443777.ics
./tests/stayfu-preloader-test.js
./tests/test-focus-behavior.js
./tests/test-pwa-data-freshness.sh
./tests/verify-auth-timing-fix.sh
./tests/verify-data-loading-fixes.sh
./.github
./.github/workflows
./.github/workflows/supabase_backup.yml
./.github/instructions
./.github/instructions/Each run.instructions.md
./.gitignore
./index.html
./tailwind.config.ts
./tsconfig.json
./vercel.json
./.augment
./.augment/env
./.augment/env/setup.sh
./jest.config.js
./postcss.config.js
./package.json
./.env.development
./CLAUDE.md
./.claude
./.claude/settings.local.json
./package-lock.json
./vite.config.js
./.gemini
./.gemini/README.md
./.gemini/file_structure.txt
