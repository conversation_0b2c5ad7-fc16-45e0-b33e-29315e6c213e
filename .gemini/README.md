# Project Context for Gemini CLI

This directory contains files to help the Gemini CLI agent understand the project structure and context for future development tasks.

## Project Overview

This project appears to be a web application built with React/TypeScript, utilizing a Supabase backend for data management and authentication. It also includes a Chrome extension.

## Key Directories and Their Purposes

- `src/`: Contains the main application source code.
    - `src/api/`: API service definitions.
    - `src/assets/`: Static assets like images.
    - `src/components/`: Reusable React components, further organized by feature/domain (e.g., `admin`, `auth`, `properties`, `damages`, `inventory`, `maintenance`, `onboarding`, `settings`, `teams`, `theme`, `ui`).
        - `src/components/admin/`: Components related to administrative functionalities (user management, database backup/restore).
        - `src/components/auth/`: Authentication-related components (login, registration, session management, permission gates).
        - `src/components/automation/`: Components for managing automation rules.
        - `src/components/common/`: Generic, reusable UI components and hooks.
        - `src/components/damages/`: Components for managing damage reports, including PDF export and invoice generation.
        - `src/components/dashboard/`: Dashboard-specific components, including AI command center.
        - `src/components/debug/`: Components for debugging data loading and permissions.
        - `src/components/inventory/`: Components for inventory management (product search, import, analytics, purchase orders).
        - `src/components/layout/`: Main application layout components (navbar, sidebar).
        - `src/components/maintenance/`: Components for managing maintenance tasks and service providers.
        - `src/components/onboarding/`: Components for user onboarding and feature tours.
        - `src/components/properties/`: Components for property management (calendar, documents, filters).
        - `src/components/pwa/`: Progressive Web App related components.
        - `src/components/settings/`: User and application settings.
        - `src/components/teams/`: Components for team management (invitations, permissions).
        - `src/components/theme/`: Theming related components.
        - `src/components/ui/`: Shadcn UI components.
    - `src/contexts/`: React Context API for global state management.
    - `src/docs/`: Internal documentation for various features and fixes.
    - `src/hooks/`: Custom React hooks.
    - `src/integrations/`: Integrations with external services (e.g., Supabase).
    - `src/lib/`: Utility functions and types.
    - `src/pages/`: Top-level page components.
    - `src/providers/`: React Context Providers.
    - `src/services/`: Business logic and data fetching services.
    - `src/test/`: Ad-hoc test scripts and markdown files.
    - `src/tests/`: Unit and integration tests.
    - `src/types/`: TypeScript type definitions.
    - `src/utils/`: General utility functions.
- `public/`: Publicly accessible assets and service worker.
- `cypress/`: End-to-end testing configuration and tests.
- `supabase/`: Supabase related configurations, migrations, and functions.
    - `supabase/functions/`: Supabase Edge Functions (Deno).
    - `supabase/migrations/`: Database migration scripts.
- `chrome/`: Chrome extension source code.
- `docs/`: Project documentation.
- `scripts/`: Various utility scripts.
- `tests/`: Additional test files (HTML, JS, shell scripts).
- `node_modules/`: Installed Node.js packages.
- Configuration files: `package.json`, `tsconfig.json`, `vite.config.js`, `tailwind.config.ts`, `jest.config.js`, `eslint.config.js`, etc.

## Technologies Used

- **Frontend:** React, TypeScript, Tailwind CSS, Shadcn UI, React Query, React Hook Form, date-fns, Embla Carousel, Framer Motion, Lucide React, Luxon, React Day Picker, React Quill, React Resizable Panels, React Router.
- **Backend (via Supabase):** PostgreSQL, Supabase Edge Functions (Deno/TypeScript).
- **Testing:** Cypress, Jest, MSW (Mock Service Worker), React Testing Library.
- **Build Tools:** Vite, Bun.
- **Other:** Git, Docker (docker-compose).

## Development Workflow Notes

- **Data Management:** Heavily relies on Supabase for database operations, authentication, and storage.
- **API Interaction:** Uses `src/api/` and `src/services/` for interacting with Supabase and other external APIs.
- **State Management:** React Context API and React Query are used for state management and data fetching.
- **Styling:** Tailwind CSS and Shadcn UI for consistent styling.
- **Testing:** Comprehensive testing setup with Cypress for E2E and Jest/React Testing Library for unit/integration tests.
- **Chrome Extension:** Separate directory for the Chrome extension, indicating a potential integration point with the main application.
- **Documentation:** Extensive `docs/` and `src/docs/` directories suggest a focus on internal documentation.

This overview should provide a solid foundation for understanding the project and performing future development tasks.
