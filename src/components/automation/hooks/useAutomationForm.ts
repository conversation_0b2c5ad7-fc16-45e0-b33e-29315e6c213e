import { useState, useCallback } from 'react';
import { AutomationRule, AutomationRuleFormData } from '../types';
import { FORM_DEFAULTS } from '../constants';
import { validateAutomationForm, formatRuleForForm, showValidationErrorToast } from '../utils';

export const useAutomationForm = () => {
  const [formData, setFormData] = useState<AutomationRuleFormData>(FORM_DEFAULTS);

  const resetForm = useCallback(() => {
    setFormData(FORM_DEFAULTS);
  }, []);

  const loadRule = useCallback((rule: AutomationRule) => {
    setFormData(formatRuleForForm(rule));
  }, []);

  const updateField = useCallback(<K extends keyof AutomationRuleFormData>(
    field: K,
    value: AutomationRuleFormData[K]
  ) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  }, []);

  const validateForm = useCallback(() => {
    const validation = validateAutomationForm(formData);
    
    if (!validation.isValid) {
      showValidationErrorToast(validation.errors);
    }
    
    return validation.isValid;
  }, [formData]);

  const updatePropertySelection = useCallback((propertyIds: string[]) => {
    updateField('property_ids', propertyIds);
  }, [updateField]);

  const togglePropertySelection = useCallback((propertyId: string) => {
    setFormData(prev => ({
      ...prev,
      property_ids: prev.property_ids.includes(propertyId)
        ? prev.property_ids.filter(id => id !== propertyId)
        : [...prev.property_ids, propertyId]
    }));
  }, []);

  const selectAllProperties = useCallback(() => {
    updateField('property_ids', []);
  }, [updateField]);

  return {
    formData,
    resetForm,
    loadRule,
    updateField,
    validateForm,
    updatePropertySelection,
    togglePropertySelection,
    selectAllProperties
  };
};