import { useState, useCallback } from 'react';
import { useTaskAutomationQueryV2 } from '@/hooks/useTaskAutomationQueryV2';
import { AutomationRuleFormData } from '../types';
import { 
  formatRuleForAPI, 
  confirmRuleDeletion, 
  showProcessingLoadingToast, 
  showProcessingSuccessToast, 
  showProcessingErrorToast 
} from '../utils';

export const useAutomationActions = () => {
  const {
    createRule,
    updateRule,
    deleteRule,
    processAllBookings
  } = useTaskAutomationQueryV2();
  
  const [isProcessing, setIsProcessing] = useState(false);

  const handleCreateRule = useCallback(async (formData: AutomationRuleFormData): Promise<boolean> => {
    const ruleData = formatRuleForAPI(formData);
    return await createRule(ruleData);
  }, [createRule]);

  const handleUpdateRule = useCallback(async (id: string, formData: AutomationRuleFormData): Promise<boolean> => {
    const ruleData = formatRuleForAPI(formData);
    return await updateRule(id, ruleData);
  }, [updateRule]);

  const handleDeleteRule = useCallback(async (id: string): Promise<boolean> => {
    if (!confirmRuleDeletion()) {
      return false;
    }
    return await deleteRule(id);
  }, [deleteRule]);

  const handleProcessAllBookings = useCallback(async (): Promise<boolean> => {
    if (isProcessing) return false;

    setIsProcessing(true);
    
    try {
      showProcessingLoadingToast();
      const success = await processAllBookings();
      
      if (success) {
        showProcessingSuccessToast();
      } else {
        showProcessingErrorToast();
      }
      
      return success;
    } catch (error) {
      console.error('[useAutomationActions] Error processing bookings:', error);
      showProcessingErrorToast('An error occurred while processing automation rules.');
      return false;
    } finally {
      setIsProcessing(false);
    }
  }, [isProcessing, processAllBookings]);

  return {
    isProcessing,
    handleCreateRule,
    handleUpdateRule,
    handleDeleteRule,
    handleProcessAllBookings
  };
};