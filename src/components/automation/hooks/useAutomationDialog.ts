import { useState, useCallback } from 'react';
import { AutomationRule } from '../types';

export const useAutomationDialog = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [editingRule, setEditingRule] = useState<AutomationRule | null>(null);

  const openCreateDialog = useCallback(() => {
    setEditingRule(null);
    setIsOpen(true);
  }, []);

  const openEditDialog = useCallback((rule: AutomationRule) => {
    setEditingRule(rule);
    setIsOpen(true);
  }, []);

  const closeDialog = useCallback(() => {
    setIsOpen(false);
    setEditingRule(null);
  }, []);

  const isEditing = editingRule !== null;

  return {
    isOpen,
    editingRule,
    isEditing,
    openCreateDialog,
    openEditDialog,
    closeDialog
  };
};