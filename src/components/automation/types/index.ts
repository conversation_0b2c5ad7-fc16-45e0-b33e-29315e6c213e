export interface AutomationRule {
  id: string;
  name: string;
  trigger_type: 'check_in' | 'check_out';
  task_type: 'cleaning' | 'inspection' | 'maintenance';
  time_offset: number;
  property_ids: string[] | null;
  title: string;
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  assigned_to: string | null;
  created_at: string;
  updated_at: string;
}

export interface AutomationRuleFormData {
  name: string;
  trigger_type: 'check_in' | 'check_out';
  task_type: 'cleaning' | 'inspection' | 'maintenance';
  time_offset: number;
  property_ids: string[];
  title: string;
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

export interface AutomationRuleDialogProps {
  isOpen: boolean;
  onClose: () => void;
  rule?: AutomationRule;
  onSave: (rule: AutomationRuleFormData) => Promise<void>;
  properties?: Property[];
}

export interface Property {
  id: string;
  name: string;
}

export interface AutomationRuleCardProps {
  rule: AutomationRule;
  onEdit: (rule: AutomationRule) => void;
  onDelete: (id: string) => void;
  properties?: Property[];
}

export interface PropertySelectorProps {
  selectedIds: string[];
  onSelectionChange: (ids: string[]) => void;
  properties?: Property[];
}

export interface AutomationActionsResult {
  success: boolean;
  error?: string;
}

export interface AutomationProcessingStatus {
  isProcessing: boolean;
  canProcess: boolean;
  rulesCount: number;
}