import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Loader2 } from 'lucide-react';
import { useTaskAutomationQueryV2 } from '@/hooks/useTaskAutomationQueryV2';
import { usePropertiesQueryV2 } from '@/hooks/usePropertiesQueryV2';
import { useAutomationDialog, useAutomationActions } from './hooks';
import { 
  AutomationHeader, 
  AutomationRulesList, 
  AutomationRuleDialog 
} from './components';
import { AutomationRuleFormData } from './types';
import { canProcessAutomation } from './utils';

const AutomationRules: React.FC = () => {
  const { rules, loading, error } = useTaskAutomationQueryV2();
  const { properties } = usePropertiesQueryV2();
  
  const {
    isOpen,
    editingRule,
    openCreateDialog,
    openEditDialog,
    closeDialog
  } = useAutomationDialog();
  
  const {
    isProcessing,
    handleCreateRule,
    handleUpdateRule,
    handleDeleteRule,
    handleProcessAllBookings
  } = useAutomationActions();

  const handleSave = async (formData: AutomationRuleFormData) => {
    if (editingRule) {
      await handleUpdateRule(editingRule.id, formData);
    } else {
      await handleCreateRule(formData);
    }
  };



  return (
    <div className="space-y-6">
      <AutomationHeader
        onAddRule={openCreateDialog}
        onProcessAll={handleProcessAllBookings}
        isProcessing={isProcessing}
        canProcess={canProcessAutomation(rules.length)}
      />

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      ) : error ? (
        <Card>
          <CardContent className="p-6">
            <p className="text-red-500">{error}</p>
          </CardContent>
        </Card>
      ) : (
        <AutomationRulesList
          rules={rules}
          onEdit={openEditDialog}
          onDelete={handleDeleteRule}
          onAddRule={openCreateDialog}
          properties={properties}
        />
      )}

      <AutomationRuleDialog
        isOpen={isOpen}
        onClose={closeDialog}
        rule={editingRule}
        onSave={handleSave}
        properties={properties}
      />
    </div>
  );
};

export default AutomationRules;
