import { toast } from 'sonner';
import { SEVERITY_COLORS, VALIDATION_MESSAGES, TOAST_MESSAGES } from '../constants';
import { AutomationRule, AutomationRuleFormData, Property } from '../types';

/**
 * Get the color class for a severity level
 */
export const getSeverityColor = (severity: string): string => {
  return SEVERITY_COLORS[severity as keyof typeof SEVERITY_COLORS] || SEVERITY_COLORS.medium;
};

/**
 * Format time offset for display
 */
export const formatTimeOffset = (offset: number): string => {
  if (offset === 0) return '';
  if (offset > 0) return ` + ${offset} hours`;
  return ` - ${Math.abs(offset)} hours`;
};

/**
 * Format property count for display
 */
export const formatPropertyCount = (propertyIds: string[] | null): string => {
  if (!propertyIds || propertyIds.length === 0) return 'All properties';
  return `${propertyIds.length} selected`;
};

/**
 * Get property names from IDs
 */
export const getPropertyNames = (propertyIds: string[] | null, properties: Property[] = []): string[] => {
  if (!propertyIds || propertyIds.length === 0) return [];
  
  return propertyIds
    .map(id => properties.find(p => p.id === id)?.name)
    .filter(Boolean) as string[];
};

/**
 * Validate automation rule form data
 */
export const validateAutomationForm = (data: AutomationRuleFormData): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  if (!data.name.trim()) {
    errors.push(VALIDATION_MESSAGES.REQUIRED_NAME);
  }

  if (!data.title.trim()) {
    errors.push(VALIDATION_MESSAGES.REQUIRED_TITLE);
  }

  if (isNaN(data.time_offset)) {
    errors.push(VALIDATION_MESSAGES.INVALID_TIME_OFFSET);
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Convert form data to automation rule data for API
 */
export const formatRuleForAPI = (formData: AutomationRuleFormData) => {
  return {
    name: formData.name,
    trigger_type: formData.trigger_type,
    task_type: formData.task_type,
    time_offset: formData.time_offset,
    property_ids: formData.property_ids.length > 0 ? formData.property_ids : null,
    title: formData.title,
    description: formData.description,
    severity: formData.severity,
    assigned_to: null
  };
};

/**
 * Convert automation rule to form data
 */
export const formatRuleForForm = (rule: AutomationRule): AutomationRuleFormData => {
  return {
    name: rule.name,
    trigger_type: rule.trigger_type,
    task_type: rule.task_type,
    time_offset: rule.time_offset,
    property_ids: rule.property_ids || [],
    title: rule.title,
    description: rule.description,
    severity: rule.severity
  };
};

/**
 * Show success toast for automation processing
 */
export const showProcessingSuccessToast = () => {
  toast({
    title: TOAST_MESSAGES.SUCCESS_TITLE,
    description: TOAST_MESSAGES.SUCCESS_DESCRIPTION,
    duration: 5000
  });
};

/**
 * Show error toast for automation processing
 */
export const showProcessingErrorToast = (customMessage?: string) => {
  toast({
    title: TOAST_MESSAGES.ERROR_TITLE,
    description: customMessage || TOAST_MESSAGES.ERROR_DESCRIPTION,
    variant: 'destructive',
    duration: 5000
  });
};

/**
 * Show loading toast for automation processing
 */
export const showProcessingLoadingToast = () => {
  toast({
    title: TOAST_MESSAGES.PROCESSING_RULES,
    description: TOAST_MESSAGES.PROCESSING_DESCRIPTION,
    duration: 10000
  });
};

/**
 * Show form validation error toast
 */
export const showValidationErrorToast = (errors: string[]) => {
  const errorMessage = errors.join(', ');
  toast.error(errorMessage);
};

/**
 * Confirm deletion of automation rule
 */
export const confirmRuleDeletion = (): boolean => {
  return window.confirm(VALIDATION_MESSAGES.CONFIRM_DELETE);
};

/**
 * Check if automation processing is available
 */
export const canProcessAutomation = (rulesCount: number): boolean => {
  return rulesCount > 0;
};

/**
 * Get trigger display text
 */
export const getTriggerDisplayText = (triggerType: string, timeOffset: number): string => {
  const baseText = triggerType === 'check_in' ? 'After check-in' : 'After check-out';
  const offsetText = formatTimeOffset(timeOffset);
  return baseText + offsetText;
};