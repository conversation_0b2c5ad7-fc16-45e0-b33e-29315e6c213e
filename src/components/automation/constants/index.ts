export const TRIGGER_TYPES = {
  CHECK_IN: 'check_in',
  CHECK_OUT: 'check_out',
} as const;

export const TASK_TYPES = {
  CLEANING: 'cleaning',
  INSPECTION: 'inspection',
  MAINTENANCE: 'maintenance',
} as const;

export const SEVERITY_LEVELS = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  CRITICAL: 'critical',
} as const;

export const SEVERITY_COLORS = {
  [SEVERITY_LEVELS.LOW]: 'bg-green-500',
  [SEVERITY_LEVELS.MEDIUM]: 'bg-yellow-500',
  [SEVERITY_LEVELS.HIGH]: 'bg-orange-500',
  [SEVERITY_LEVELS.CRITICAL]: 'bg-red-500',
} as const;

export const TRIGGER_LABELS = {
  [TRIGGER_TYPES.CHECK_IN]: 'After Check-in',
  [TRIGGER_TYPES.CHECK_OUT]: 'After Check-out',
} as const;

export const TASK_TYPE_LABELS = {
  [TASK_TYPES.CLEANING]: 'Cleaning',
  [TASK_TYPES.INSPECTION]: 'Inspection',
  [TASK_TYPES.MAINTENANCE]: 'Maintenance',
} as const;

export const SEVERITY_LABELS = {
  [SEVERITY_LEVELS.LOW]: 'Low',
  [SEVERITY_LEVELS.MEDIUM]: 'Medium',
  [SEVERITY_LEVELS.HIGH]: 'High',
  [SEVERITY_LEVELS.CRITICAL]: 'Critical',
} as const;

export const FORM_DEFAULTS = {
  name: '',
  trigger_type: TRIGGER_TYPES.CHECK_OUT,
  task_type: TASK_TYPES.CLEANING,
  time_offset: 0,
  property_ids: [],
  title: '',
  description: '',
  severity: SEVERITY_LEVELS.MEDIUM,
} as const;

export const VALIDATION_MESSAGES = {
  REQUIRED_NAME: 'Name is required',
  REQUIRED_TITLE: 'Title is required',
  INVALID_TIME_OFFSET: 'Time offset must be a valid number',
  CONFIRM_DELETE: 'Are you sure you want to delete this automation rule?',
} as const;

export const TOAST_MESSAGES = {
  PROCESSING_RULES: 'Processing automation rules...',
  PROCESSING_DESCRIPTION: 'Please wait while we process all bookings.',
  SUCCESS_TITLE: 'Success!',
  SUCCESS_DESCRIPTION: 'Automation rules processed successfully. Tasks have been created.',
  ERROR_TITLE: 'Error',
  ERROR_DESCRIPTION: 'Failed to process automation rules. Please try again.',
  ERROR_PROCESSING: 'An error occurred while processing automation rules.',
} as const;

export const PLACEHOLDER_TEXTS = {
  RULE_NAME: 'e.g., Post-checkout cleaning',
  TASK_TITLE: 'e.g., Clean {property}',
  TASK_DESCRIPTION: 'e.g., Standard cleaning for {property}',
  PROPERTY_PLACEHOLDER: 'Use {property} to include the property name',
} as const;