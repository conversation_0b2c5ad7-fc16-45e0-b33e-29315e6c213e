import React from 'react';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { PropertySelectorProps } from '../types';

export const PropertySelector: React.FC<PropertySelectorProps> = ({
  selectedIds,
  onSelectionChange,
  properties = []
}) => {
  const handleSelectionChange = (value: string) => {
    if (value === 'all') {
      onSelectionChange([]);
    }
  };

  const toggleProperty = (propertyId: string, checked: boolean) => {
    if (checked) {
      onSelectionChange([...selectedIds, propertyId]);
    } else {
      onSelectionChange(selectedIds.filter(id => id !== propertyId));
    }
  };

  return (
    <div className="space-y-2">
      <Label htmlFor="properties">Properties (leave empty for all)</Label>
      <Select
        value={selectedIds.length > 0 ? 'selected' : 'all'}
        onValueChange={handleSelectionChange}
      >
        <SelectTrigger id="properties">
          <SelectValue placeholder="Select properties" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">All Properties</SelectItem>
          <SelectItem value="selected" disabled={selectedIds.length === 0}>
            {selectedIds.length} Properties Selected
          </SelectItem>
        </SelectContent>
      </Select>

      {properties && properties.length > 0 && (
        <div className="mt-2 max-h-32 overflow-y-auto border rounded-md p-2">
          {properties.map(property => (
            <div key={property.id} className="flex items-center space-x-2 py-1">
              <input
                type="checkbox"
                id={`property-${property.id}`}
                checked={selectedIds.includes(property.id)}
                onChange={(e) => toggleProperty(property.id, e.target.checked)}
              />
              <label htmlFor={`property-${property.id}`}>{property.name}</label>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};