import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Edit, Trash2 } from 'lucide-react';
import { AutomationRuleCardProps } from '../types';
import { getSeverityColor, formatPropertyCount, getTriggerDisplayText } from '../utils';
import { TASK_TYPE_LABELS, SEVERITY_LABELS } from '../constants';

type TaskType = 'cleaning' | 'inspection' | 'maintenance';
type SeverityLevel = 'low' | 'medium' | 'high' | 'critical';

export const AutomationRuleCard: React.FC<AutomationRuleCardProps> = ({
  rule,
  onEdit,
  onDelete,
  properties = []
}) => {
  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-start">
          <CardTitle>{rule.name}</CardTitle>
          <div className="flex gap-1">
            <Button variant="ghost" size="sm" onClick={() => onEdit(rule)}>
              <Edit className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm" onClick={() => onDelete(rule.id)}>
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </div>
        <CardDescription>
          {getTriggerDisplayText(rule.trigger_type, rule.time_offset)}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          <div>
            <span className="font-medium">Task Type:</span> {TASK_TYPE_LABELS[rule.task_type as TaskType]}
          </div>
          <div>
            <span className="font-medium">Title:</span> {rule.title}
          </div>
          {rule.description && (
            <div>
              <span className="font-medium">Description:</span> {rule.description}
            </div>
          )}
          <div>
            <span className="font-medium">Severity:</span>
            <Badge className={getSeverityColor(rule.severity)}>
              {SEVERITY_LABELS[rule.severity as SeverityLevel]}
            </Badge>
          </div>
          <div>
            <span className="font-medium">Properties:</span>{' '}
            {formatPropertyCount(rule.property_ids)}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};