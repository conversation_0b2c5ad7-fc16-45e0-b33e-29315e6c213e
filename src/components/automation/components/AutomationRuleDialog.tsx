import React, { useEffect } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { AutomationRuleForm } from './AutomationRuleForm';
import { useAutomationForm } from '../hooks';
import { AutomationRuleDialogProps } from '../types';

export const AutomationRuleDialog: React.FC<AutomationRuleDialogProps> = ({
  isOpen,
  onClose,
  rule,
  onSave,
  properties = []
}) => {
  const { formData, resetForm, loadRule, updateField, validateForm } = useAutomationForm();

  useEffect(() => {
    if (isOpen) {
      if (rule) {
        loadRule(rule);
      } else {
        resetForm();
      }
    }
  }, [isOpen, rule, loadRule, resetForm]);

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    await onSave(formData);
    onClose();
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>
            {rule ? 'Edit Automation Rule' : 'Create Automation Rule'}
          </DialogTitle>
        </DialogHeader>
        <AutomationRuleForm
          formData={formData}
          onFieldChange={updateField}
          properties={properties}
        />
        <DialogFooter>
          <Button variant="outline" onClick={handleClose}>
            Cancel
          </Button>
          <Button onClick={handleSubmit}>
            {rule ? 'Update' : 'Create'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};