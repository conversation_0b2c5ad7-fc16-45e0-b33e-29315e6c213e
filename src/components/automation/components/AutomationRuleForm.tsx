import React from 'react';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { PropertySelector } from './PropertySelector';
import { AutomationRuleFormData, Property } from '../types';
import { 
  TRIGGER_TYPES, 
  TASK_TYPES, 
  SEVERITY_LEVELS, 
  TRIGGER_LABELS, 
  TASK_TYPE_LABELS, 
  SEVERITY_LABELS, 
  PLACEHOLDER_TEXTS 
} from '../constants';

interface AutomationRuleFormProps {
  formData: AutomationRuleFormData;
  onFieldChange: <K extends keyof AutomationRuleFormData>(field: K, value: AutomationRuleFormData[K]) => void;
  properties?: Property[];
}

export const AutomationRuleForm: React.FC<AutomationRuleFormProps> = ({
  formData,
  onFieldChange,
  properties = []
}) => {
  return (
    <div className="space-y-4 py-4">
      <div className="space-y-2">
        <Label htmlFor="name">Rule Name</Label>
        <Input
          id="name"
          value={formData.name}
          onChange={(e) => onFieldChange('name', e.target.value)}
          placeholder={PLACEHOLDER_TEXTS.RULE_NAME}
        />
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="trigger-type">Trigger</Label>
          <Select
            value={formData.trigger_type}
            onValueChange={(value) => onFieldChange('trigger_type', value as keyof typeof TRIGGER_TYPES)}
          >
            <SelectTrigger id="trigger-type">
              <SelectValue placeholder="Select trigger" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value={TRIGGER_TYPES.CHECK_IN}>{TRIGGER_LABELS[TRIGGER_TYPES.CHECK_IN]}</SelectItem>
              <SelectItem value={TRIGGER_TYPES.CHECK_OUT}>{TRIGGER_LABELS[TRIGGER_TYPES.CHECK_OUT]}</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="time-offset">Time Offset (hours)</Label>
          <Input
            id="time-offset"
            type="number"
            value={formData.time_offset}
            onChange={(e) => onFieldChange('time_offset', parseInt(e.target.value) || 0)}
            placeholder="0"
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="task-type">Task Type</Label>
        <Select
          value={formData.task_type}
          onValueChange={(value) => onFieldChange('task_type', value as keyof typeof TASK_TYPES)}
        >
          <SelectTrigger id="task-type">
            <SelectValue placeholder="Select task type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value={TASK_TYPES.CLEANING}>{TASK_TYPE_LABELS[TASK_TYPES.CLEANING]}</SelectItem>
            <SelectItem value={TASK_TYPES.INSPECTION}>{TASK_TYPE_LABELS[TASK_TYPES.INSPECTION]}</SelectItem>
            <SelectItem value={TASK_TYPES.MAINTENANCE}>{TASK_TYPE_LABELS[TASK_TYPES.MAINTENANCE]}</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <PropertySelector
        selectedIds={formData.property_ids}
        onSelectionChange={(ids) => onFieldChange('property_ids', ids)}
        properties={properties}
      />

      <div className="space-y-2">
        <Label htmlFor="title">Task Title</Label>
        <Input
          id="title"
          value={formData.title}
          onChange={(e) => onFieldChange('title', e.target.value)}
          placeholder={PLACEHOLDER_TEXTS.TASK_TITLE}
        />
        <p className="text-xs text-muted-foreground">
          {PLACEHOLDER_TEXTS.PROPERTY_PLACEHOLDER}
        </p>
      </div>

      <div className="space-y-2">
        <Label htmlFor="description">Task Description (optional)</Label>
        <Textarea
          id="description"
          value={formData.description}
          onChange={(e) => onFieldChange('description', e.target.value)}
          placeholder={PLACEHOLDER_TEXTS.TASK_DESCRIPTION}
          rows={3}
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="severity">Severity</Label>
        <Select
          value={formData.severity}
          onValueChange={(value) => onFieldChange('severity', value as keyof typeof SEVERITY_LEVELS)}
        >
          <SelectTrigger id="severity">
            <SelectValue placeholder="Select severity" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value={SEVERITY_LEVELS.LOW}>{SEVERITY_LABELS[SEVERITY_LEVELS.LOW]}</SelectItem>
            <SelectItem value={SEVERITY_LEVELS.MEDIUM}>{SEVERITY_LABELS[SEVERITY_LEVELS.MEDIUM]}</SelectItem>
            <SelectItem value={SEVERITY_LEVELS.HIGH}>{SEVERITY_LABELS[SEVERITY_LEVELS.HIGH]}</SelectItem>
            <SelectItem value={SEVERITY_LEVELS.CRITICAL}>{SEVERITY_LABELS[SEVERITY_LEVELS.CRITICAL]}</SelectItem>
          </SelectContent>
        </Select>
      </div>
    </div>
  );
};