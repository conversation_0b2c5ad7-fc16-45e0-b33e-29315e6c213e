import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import { AutomationRuleCard } from './AutomationRuleCard';
import { AutomationRule, Property } from '../types';

interface AutomationRulesListProps {
  rules: AutomationRule[];
  onEdit: (rule: AutomationRule) => void;
  onDelete: (id: string) => void;
  onAddRule: () => void;
  properties?: Property[];
}

export const AutomationRulesList: React.FC<AutomationRulesListProps> = ({
  rules,
  onEdit,
  onDelete,
  onAddRule,
  properties = []
}) => {
  if (rules.length === 0) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <p className="text-muted-foreground">
            No automation rules found. Create your first rule to get started.
          </p>
          <Button className="mt-4" onClick={onAddRule}>
            <Plus className="h-4 w-4 mr-2" />
            Add Rule
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {rules.map(rule => (
        <AutomationRuleCard
          key={rule.id}
          rule={rule}
          onEdit={onEdit}
          onDelete={onDelete}
          properties={properties}
        />
      ))}
    </div>
  );
};