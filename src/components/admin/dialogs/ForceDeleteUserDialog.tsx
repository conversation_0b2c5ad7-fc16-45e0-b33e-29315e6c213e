import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { AlertTriangle } from 'lucide-react';

interface ForceDeleteUserDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  userEmail: string;
  isLoading?: boolean;
}

const ForceDeleteUserDialog: React.FC<ForceDeleteUserDialogProps> = ({
  isOpen,
  onClose,
  onConfirm,
  userEmail,
  isLoading = false
}) => {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-red-600">
            <AlertTriangle className="h-5 w-5" />
            Force Delete User
          </DialogTitle>
          <DialogDescription className="mb-4">
            WARNING: This is a destructive operation for corrupted accounts only.
          </DialogDescription>
          <div className="space-y-2 text-sm">
            <p>
              Force delete will bypass normal constraints and remove all data associated with {userEmail}.
              This should only be used when normal deletion fails due to database constraints or corruption.
            </p>
            <p className="font-semibold">
              This action cannot be undone and may result in orphaned data.
            </p>
          </div>
        </DialogHeader>
        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button variant="destructive" onClick={onConfirm} disabled={isLoading}>
            {isLoading ? 'Force Deleting...' : 'Force Delete User'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ForceDeleteUserDialog;