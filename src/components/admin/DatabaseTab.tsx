import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import BackupSection from './database/BackupSection';
import RestoreSection from './database/RestoreSection';

const DatabaseTab: React.FC = () => {

  return (
    <Tabs defaultValue="backup">
      <TabsList className="mb-4">
        <TabsTrigger value="backup">Backup</TabsTrigger>
        <TabsTrigger value="restore">Restore</TabsTrigger>
      </TabsList>
      
      <TabsContent value="backup">
        <BackupSection />
      </TabsContent>
      
      <TabsContent value="restore">
        <RestoreSection />
      </TabsContent>
    </Tabs>
  );
};

export default DatabaseTab;
