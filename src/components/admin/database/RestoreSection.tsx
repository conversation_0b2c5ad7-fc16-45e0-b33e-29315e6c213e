import React, { useState } from 'react';
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { UploadCloud, AlertTriangle, Loader2, Eye } from 'lucide-react';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { readFileAsJson, formatFileSize } from '../utils';
import { useAdminActions } from '../hooks/useAdminActions';
import { RestorePreviewResult } from '../types';
import RestorePreviewDialog from './RestorePreviewDialog';

interface RestoreSectionProps {
  onRestoreComplete?: () => void;
}

const RestoreSection: React.FC<RestoreSectionProps> = ({ onRestoreComplete }) => {
  const { authState } = useAuth();
  const { executeAction, isLoading } = useAdminActions();
  const [backupFile, setBackupFile] = useState<File | null>(null);
  const [previewResult, setPreviewResult] = useState<RestorePreviewResult | null>(null);
  const [showPreviewDialog, setShowPreviewDialog] = useState(false);
  const [showRestoreConfirmDialog, setShowRestoreConfirmDialog] = useState(false);
  const [isPreviewLoading, setIsPreviewLoading] = useState(false);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setBackupFile(e.target.files[0]);
      setPreviewResult(null);
    }
  };

  const handlePreviewRestore = async () => {
    if (!backupFile) {
      throw new Error('Please select a backup file to preview');
    }

    setIsPreviewLoading(true);
    try {
      const fileContents = await readFileAsJson(backupFile);
      if (!fileContents || !fileContents.tables) {
        throw new Error('Invalid backup file format');
      }

      const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
      if (sessionError) throw sessionError;
      
      const token = sessionData.session?.access_token;
      if (!token) throw new Error('Authentication required');
      
      const { data, error } = await supabase.functions.invoke('admin-restore-database', {
        headers: {
          Authorization: `Bearer ${token}`
        },
        body: { 
          previewMode: true,
          fileContents
        }
      });
      
      if (error) throw error;
      
      setPreviewResult(data as RestorePreviewResult);
      setShowPreviewDialog(true);
    } finally {
      setIsPreviewLoading(false);
    }
  };

  const handleRestoreDatabase = async () => {
    if (!authState?.profile?.is_super_admin || !backupFile) {
      throw new Error('Insufficient permissions or missing backup file');
    }

    const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
    if (sessionError) throw sessionError;
    
    const token = sessionData.session?.access_token;
    if (!token) throw new Error('Authentication required');
    
    const { data: uploadUrlData, error: uploadUrlError } = await supabase.functions.invoke('admin-get-upload-url', {
      headers: {
        Authorization: `Bearer ${token}`
      },
      body: { fileName: backupFile.name }
    });
    
    if (uploadUrlError) throw uploadUrlError;
    
    const uploadResponse = await fetch(uploadUrlData.uploadUrl, {
      method: 'PUT',
      body: backupFile,
      headers: {
        'Content-Type': 'application/json',
      }
    });
    
    if (!uploadResponse.ok) {
      throw new Error(`Failed to upload backup file: ${uploadResponse.statusText}`);
    }
    
    const { data, error } = await supabase.functions.invoke('admin-restore-database', {
      headers: {
        Authorization: `Bearer ${token}`
      },
      body: { 
        fileKey: uploadUrlData.fileKey,
        previewMode: false
      }
    });
    
    if (error) throw error;
    
    setBackupFile(null);
    onRestoreComplete?.();
  };

  const performPreview = () => {
    executeAction(
      handlePreviewRestore,
      'Backup preview completed successfully',
      'Failed to preview restore'
    );
  };

  const performRestore = () => {
    setShowRestoreConfirmDialog(false);
    executeAction(
      handleRestoreDatabase,
      'Database restore completed successfully. You may need to refresh the page to see changes.',
      'Failed to restore database'
    );
  };

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <UploadCloud className="h-5 w-5" />
            Database Restore
          </CardTitle>
          <CardDescription>
            Restore your database from a previous backup
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center p-4 bg-destructive/10 text-destructive rounded-md">
            <AlertTriangle className="h-5 w-5 mr-2 flex-shrink-0" />
            <p className="text-sm">
              <strong>Warning:</strong> Restoring a database will overwrite your current data. 
              It's recommended to first use the Preview feature to validate your backup file before proceeding.
            </p>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="restore-file">Backup File</Label>
            <Input 
              id="restore-file" 
              type="file" 
              accept=".json"
              onChange={handleFileChange}
            />
            <p className="text-xs text-muted-foreground">
              Only JSON backup files created by this tool are supported for restore via this interface.
            </p>
            {backupFile && (
              <p className="text-sm text-muted-foreground mt-1">
                Selected file: {backupFile.name} ({formatFileSize(backupFile.size)})
              </p>
            )}
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button
            onClick={performPreview}
            disabled={isPreviewLoading || !backupFile || !authState?.profile?.is_super_admin}
            variant="outline"
          >
            {isPreviewLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Analyzing Backup...
              </>
            ) : (
              <>
                <Eye className="mr-2 h-4 w-4" />
                Preview Restore
              </>
            )}
          </Button>

          <AlertDialog open={showRestoreConfirmDialog} onOpenChange={setShowRestoreConfirmDialog}>
            <AlertDialogTrigger asChild>
              <Button
                disabled={isLoading || !backupFile || !authState?.profile?.is_super_admin}
                variant="destructive"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Restoring...
                  </>
                ) : (
                  <>
                    <UploadCloud className="mr-2 h-4 w-4" />
                    Restore Database
                  </>
                )}
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                <AlertDialogDescription>
                  This action cannot be undone. This will permanently overwrite your
                  current database with the data from the backup file{' '}
                  <strong>{backupFile?.name}</strong>.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel disabled={isLoading}>Cancel</AlertDialogCancel>
                <AlertDialogAction 
                  onClick={performRestore} 
                  disabled={isLoading}
                  className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Restoring...
                    </>
                  ) : (
                    'Yes, restore database'
                  )}
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </CardFooter>
      </Card>
      
      <RestorePreviewDialog
        isOpen={showPreviewDialog}
        onClose={() => setShowPreviewDialog(false)}
        previewResult={previewResult}
        onProceedToRestore={() => {
          setShowPreviewDialog(false);
          setShowRestoreConfirmDialog(true);
        }}
      />
    </>
  );
};

export default RestoreSection;