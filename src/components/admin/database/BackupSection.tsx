import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>eader, CardTitle, CardDescription, CardContent, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { DownloadCloud, Info, Loader2, FileJson, FileCode } from 'lucide-react';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { downloadFile } from '../utils';
import { useAdminActions } from '../hooks/useAdminActions';

interface BackupSectionProps {
  onBackupComplete?: () => void;
}

const BackupSection: React.FC<BackupSectionProps> = ({ onBackupComplete }) => {
  const { authState } = useAuth();
  const { executeAction, isLoading } = useAdminActions();
  const [backupName, setBackupName] = useState(`backup-${new Date().toISOString().split('T')[0]}`);
  const [backupFormat, setBackupFormat] = useState<'json' | 'sql'>('json');

  const handleBackup = async () => {
    if (!authState?.profile?.is_super_admin) {
      throw new Error('Only super admins can perform database backups');
    }

    const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
    if (sessionError) throw sessionError;
    
    const token = sessionData.session?.access_token;
    if (!token) throw new Error('Authentication required');
    
    const { data, error } = await supabase.functions.invoke('admin-backup-database', {
      headers: {
        Authorization: `Bearer ${token}`
      },
      body: { 
        backupName,
        format: backupFormat
      }
    });
    
    if (error) throw error;
    
    if (backupFormat === 'json') {
      const jsonString = JSON.stringify(data, null, 2);
      downloadFile(jsonString, `${backupName}.json`, 'application/json');
    } else {
      if (!data?.sql) {
        throw new Error('SQL data is missing from the response');
      }
      downloadFile(data.sql, `${backupName}.sql`, 'text/plain');
    }
    
    onBackupComplete?.();
  };

  const performBackup = () => {
    executeAction(
      handleBackup,
      `Database backup created successfully as ${backupFormat.toUpperCase()}`,
      'Failed to create backup'
    );
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <DownloadCloud className="h-5 w-5" />
          Database Backup
        </CardTitle>
        <CardDescription>
          Create a backup of your database schema and data
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center p-4 bg-amber-50 text-amber-800 rounded-md">
          <Info className="h-5 w-5 mr-2 flex-shrink-0" />
          <p className="text-sm">
            Database backups include all tables, data, and schema information. 
            Store these backups securely as they contain sensitive information.
          </p>
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="backup-name">Backup Name</Label>
          <Input 
            id="backup-name" 
            value={backupName} 
            onChange={(e) => setBackupName(e.target.value)}
            placeholder="Enter a name for this backup"
          />
        </div>

        <div className="space-y-2">
          <Label>Backup Format</Label>
          <RadioGroup 
            value={backupFormat} 
            onValueChange={(value) => setBackupFormat(value as 'json' | 'sql')} 
            className="flex space-x-4"
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="json" id="json-format" />
              <Label htmlFor="json-format" className="flex items-center">
                <FileJson className="h-4 w-4 mr-2" />
                JSON (Full Data)
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="sql" id="sql-format" />
              <Label htmlFor="sql-format" className="flex items-center">
                <FileCode className="h-4 w-4 mr-2" />
                SQL (Direct Import)
              </Label>
            </div>
          </RadioGroup>
          <p className="text-sm text-muted-foreground">
            {backupFormat === 'json' 
              ? 'JSON format contains all data and is used by this application\'s restore function.' 
              : 'SQL format can be directly imported into any PostgreSQL database, including Supabase.'}
          </p>
        </div>
      </CardContent>
      <CardFooter>
        <Button
          onClick={performBackup}
          disabled={isLoading || !authState?.profile?.is_super_admin}
          className="ml-auto"
        >
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Creating Backup...
            </>
          ) : (
            <>
              <DownloadCloud className="mr-2 h-4 w-4" />
              Create Backup
            </>
          )}
        </Button>
      </CardFooter>
    </Card>
  );
};

export default BackupSection;