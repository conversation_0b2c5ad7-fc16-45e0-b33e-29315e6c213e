import React from 'react';
import { <PERSON><PERSON>, DialogContent, DialogDescription, Di<PERSON><PERSON>ooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { RestorePreviewResult } from '../types';

interface RestorePreviewDialogProps {
  isOpen: boolean;
  onClose: () => void;
  previewResult: RestorePreviewResult | null;
  onProceedToRestore: () => void;
}

const RestorePreviewDialog: React.FC<RestorePreviewDialogProps> = ({
  isOpen,
  onClose,
  previewResult,
  onProceedToRestore
}) => {
  if (!previewResult) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl">
        <DialogHeader>
          <DialogTitle>Backup Preview Results</DialogTitle>
          <DialogDescription>
            This is a preview of what would happen if you restore this backup file.
            No changes have been made to your database.
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4">
          <div className="flex gap-4 flex-wrap">
            <div className="bg-muted p-3 rounded-md">
              <p className="text-sm font-medium">Tables Found</p>
              <p className="text-2xl font-bold">{previewResult.tablesFound}</p>
            </div>
            
            <div className="bg-muted p-3 rounded-md">
              <p className="text-sm font-medium">Status</p>
              <Badge variant={previewResult.success ? "default" : "destructive"}>
                {previewResult.success ? "Valid" : "Issues Found"}
              </Badge>
            </div>
            
            <div className="bg-muted p-3 rounded-md">
              <p className="text-sm font-medium">Contains Data</p>
              <Badge variant={previewResult.hasData ? "default" : "secondary"}>
                {previewResult.hasData ? "Yes" : "No"}
              </Badge>
            </div>
          </div>
          
          <ScrollArea className="h-[350px] rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Table Name</TableHead>
                  <TableHead>Exists</TableHead>
                  <TableHead>Columns Match</TableHead>
                  <TableHead>Rows</TableHead>
                  <TableHead>Status</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {Object.entries(previewResult.tables).map(([tableName, status]) => (
                  <TableRow key={tableName}>
                    <TableCell className="font-medium">{tableName}</TableCell>
                    <TableCell>
                      <Badge variant={status.exists ? "default" : "destructive"}>
                        {status.exists ? "Yes" : "No"}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {status.columnsMatch !== undefined ? (
                        <Badge variant={status.columnsMatch ? "default" : "destructive"}>
                          {status.columnsMatch ? "Compatible" : "Mismatch"}
                        </Badge>
                      ) : (
                        <Badge variant="secondary">Not Checked</Badge>
                      )}
                    </TableCell>
                    <TableCell>{status.dataRows}</TableCell>
                    <TableCell>
                      {status.error ? (
                        <div className="text-destructive max-w-[200px] text-xs">{status.error}</div>
                      ) : (
                        <Badge variant="default">Ready</Badge>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </ScrollArea>
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={onClose}>Close</Button>
          {previewResult.success && (
            <Button variant="default" onClick={onProceedToRestore}>
              Proceed to Restore Confirmation
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default RestorePreviewDialog;