import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Shield<PERSON>lert, UserCog, Loader2 } from 'lucide-react';
import { UserProfile } from '@/types/auth';
import { formatUserName } from './index';
import UserManagement from '../UserManagement';

export const createUserTableColumns = (
  currentUserId?: string,
  impersonatingUserId?: string | null,
  onImpersonate?: (userId: string) => void,
  onUserUpdated?: () => void
) => [
  {
    header: 'Name',
    accessor: (user: UserProfile) => (
      <div className="flex items-center gap-2">
        {user.is_super_admin && <ShieldAlert className="h-4 w-4 text-amber-500" />}
        {formatUserName(user.first_name, user.last_name)}
      </div>
    ),
    className: 'font-medium'
  },
  {
    header: 'Email',
    accessor: 'email' as keyof UserProfile
  },
  {
    header: 'Role',
    accessor: (user: UserProfile) => (
      <Badge variant={user.is_super_admin ? "destructive" : "secondary"}>
        {user.is_super_admin ? 'Super Admin' : user.role || 'User'}
      </Badge>
    )
  },
  {
    header: 'Status',
    accessor: () => (
      <div className="flex items-center">
        <span
          className="h-2 w-2 rounded-full bg-green-500 mr-2"
          aria-label="Active status"
        ></span>
        Active
      </div>
    )
  },
  {
    header: 'Impersonate',
    accessor: (user: UserProfile) => (
      user.id !== currentUserId && onImpersonate ? (
        <Button
          variant="ghost"
          size="sm"
          className="h-8 px-2 text-muted-foreground hover:text-foreground flex items-center gap-1"
          onClick={() => onImpersonate(user.id)}
          title="Impersonate user"
          disabled={impersonatingUserId === user.id}
        >
          {impersonatingUserId === user.id ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <UserCog className="h-4 w-4" />
          )}
          <span className="sr-only md:not-sr-only md:inline-block">
            {impersonatingUserId === user.id ? 'Loading...' : 'Impersonate'}
          </span>
        </Button>
      ) : (
        <span className="text-xs text-muted-foreground">Current user</span>
      )
    )
  },
  {
    header: 'Manage',
    accessor: (user: UserProfile) => (
      <UserManagement user={user} onUserUpdated={onUserUpdated || (() => {})} />
    ),
    className: 'text-right'
  }
];