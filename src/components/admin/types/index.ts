export interface TableStatus {
  exists: boolean;
  dataRows: number;
  columnsMatch: boolean;
  error: string | null;
  restored?: boolean;
}

export interface RestorePreviewResult {
  tables: Record<string, TableStatus>;
  success: boolean;
  previewMode: boolean;
  tablesFound: number;
  hasData: boolean;
  message: string;
}

export interface AdminDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  isLoading?: boolean;
}

export interface BackupFormat {
  type: 'json' | 'sql';
  label: string;
  description: string;
  icon: string;
}