import { supabase } from '@/integrations/supabase/client';

export const useSupabaseSession = () => {
  const getSessionToken = async (): Promise<string> => {
    const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
    if (sessionError) throw sessionError;
    
    const token = sessionData.session?.access_token;
    if (!token) throw new Error('Authentication required');
    
    return token;
  };

  const invokeFunction = async (functionName: string, body: any) => {
    const token = await getSessionToken();
    
    const { data, error } = await supabase.functions.invoke(functionName, {
      headers: {
        Authorization: `Bearer ${token}`
      },
      body
    });
    
    if (error) throw error;
    return data;
  };

  return {
    getSessionToken,
    invokeFunction
  };
};