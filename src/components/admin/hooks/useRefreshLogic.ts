import { useState, useCallback } from 'react';

export const useRefreshLogic = (refreshFn: () => Promise<void>) => {
  const [isRefreshing, setIsRefreshing] = useState(false);

  const handleRefresh = useCallback(async () => {
    setIsRefreshing(true);
    try {
      await refreshFn();
    } finally {
      setIsRefreshing(false);
    }
  }, [refreshFn]);

  return { isRefreshing, handleRefresh };
};