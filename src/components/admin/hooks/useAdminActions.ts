import { useState } from 'react';
import { toast } from 'sonner';

export const useAdminActions = () => {
  const [isLoading, setIsLoading] = useState(false);

  const executeAction = async (
    action: () => Promise<any>,
    successMessage: string,
    errorMessage: string = 'Operation failed'
  ) => {
    setIsLoading(true);
    try {
      const result = await action();
      toast.success(successMessage);
      return result;
    } catch (error: any) {
      console.error('Admin action failed:', error);
      toast.error(`${errorMessage}: ${error.message || 'Unknown error'}`);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  return { executeAction, isLoading };
};