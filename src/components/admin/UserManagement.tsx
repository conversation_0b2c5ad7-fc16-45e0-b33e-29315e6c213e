import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { toast } from 'sonner';
import { Pencil, Trash2, Key, AlertTriangle } from 'lucide-react';
import { UserProfile } from '@/types/auth';
import useAdmin from '@/hooks/useAdmin';
import EditUserDialog from './dialogs/EditUserDialog';
import ResetPasswordDialog from './dialogs/ResetPasswordDialog';
import DeleteUserDialog from './dialogs/DeleteUserDialog';
import ForceDeleteUserDialog from './dialogs/ForceDeleteUserDialog';

interface UserManagementProps {
  user: UserProfile;
  onUserUpdated: () => void;
}

const UserManagement: React.FC<UserManagementProps> = ({ user, onUserUpdated }) => {
  const { updateUser, deleteUser, forceDeleteUser, resetUserPassword, isLoading } = useAdmin();

  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isPasswordDialogOpen, setIsPasswordDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isForceDeleteDialogOpen, setIsForceDeleteDialogOpen] = useState(false);

  const handleUpdateUser = async (userData: Partial<UserProfile>) => {
    const success = await updateUser(user.id, userData);
    if (success) {
      setIsEditDialogOpen(false);
      onUserUpdated();
    }
  };

  const handleUpdatePassword = async (password: string) => {
    const success = await resetUserPassword(user.id, password);
    if (success) {
      setIsPasswordDialogOpen(false);
    }
  };

  const handleDeleteUser = async () => {
    const success = await deleteUser(user.id);

    if (success) {
      setIsDeleteDialogOpen(false);
      onUserUpdated();
    } else {
      // If deletion fails, suggest force delete
      toast.error('This user account may be corrupted. Try using Force Delete instead.', {
        duration: 5000,
        action: {
          label: 'Force Delete',
          onClick: () => setIsForceDeleteDialogOpen(true)
        }
      });
    }
  };

  const handleForceDeleteUser = async () => {
    const success = await forceDeleteUser(user.id);

    if (success) {
      setIsForceDeleteDialogOpen(false);
      onUserUpdated();
    }
  };

  return (
    <>
      <div className="flex space-x-2">
        <Button
          variant="outline"
          size="sm"
          onClick={() => setIsEditDialogOpen(true)}
          title="Edit User"
        >
          <Pencil className="h-4 w-4" />
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => setIsPasswordDialogOpen(true)}
          title="Reset Password"
        >
          <Key className="h-4 w-4" />
        </Button>
        <Button
          variant="destructive"
          size="sm"
          onClick={() => setIsDeleteDialogOpen(true)}
          title="Delete User"
        >
          <Trash2 className="h-4 w-4" />
        </Button>
        <Button
          variant="destructive"
          size="sm"
          onClick={() => setIsForceDeleteDialogOpen(true)}
          title="Force Delete User (for corrupted accounts)"
          className={user.first_name === 'User Imported' ? 'flex' : 'hidden group-hover:flex'}
        >
          <AlertTriangle className="h-4 w-4" />
        </Button>
      </div>

      <EditUserDialog
        isOpen={isEditDialogOpen}
        onClose={() => setIsEditDialogOpen(false)}
        onConfirm={handleUpdateUser}
        user={user}
        isLoading={isLoading}
      />

      <ResetPasswordDialog
        isOpen={isPasswordDialogOpen}
        onClose={() => setIsPasswordDialogOpen(false)}
        onConfirm={handleUpdatePassword}
        userEmail={user.email}
        isLoading={isLoading}
      />

      <DeleteUserDialog
        isOpen={isDeleteDialogOpen}
        onClose={() => setIsDeleteDialogOpen(false)}
        onConfirm={handleDeleteUser}
        userEmail={user.email}
        isLoading={isLoading}
      />

      <ForceDeleteUserDialog
        isOpen={isForceDeleteDialogOpen}
        onClose={() => setIsForceDeleteDialogOpen(false)}
        onConfirm={handleForceDeleteUser}
        userEmail={user.email}
        isLoading={isLoading}
      />
    </>
  );
};

export default UserManagement;
