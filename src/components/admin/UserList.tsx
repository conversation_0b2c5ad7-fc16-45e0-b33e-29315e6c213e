
import React, { useState } from 'react';
import { UserProfile } from '@/types/auth';
import { Button } from '@/components/ui/button';
import { RefreshCw, Loader2 } from 'lucide-react';
import { toast } from 'sonner';
import AdminTable from './AdminTable';
import { createUserTableColumns } from './utils/userTableColumns';
import { useRefreshLogic } from './hooks/useRefreshLogic';

interface UserListProps {
  users: UserProfile[];
  isLoading: boolean;
  isRefreshing: boolean;
  currentUserId?: string;
  onRefresh: () => Promise<void>;
  onImpersonate: (userId: string) => Promise<void>;
  onUserUpdated: () => Promise<void>;
}

const UserList: React.FC<UserListProps> = ({
  users,
  isLoading,
  isRefreshing,
  currentUserId,
  onRefresh,
  onImpersonate,
  onUserUpdated
}) => {
  const [impersonatingUserId, setImpersonatingUserId] = useState<string | null>(null);
  const { isRefreshing: isRefreshingLocal, handleRefresh } = useRefreshLogic(onRefresh);

  const handleImpersonateClick = async (userId: string) => {
    setImpersonatingUserId(userId);
    try {
      console.log("Starting impersonation for user:", userId);
      await onImpersonate(userId);
    } catch (error) {
      console.error("Impersonation failed:", error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      toast.error(`Failed to impersonate user: ${errorMessage}`);
    } finally {
      setImpersonatingUserId(null);
    }
  };

  const columns = createUserTableColumns(
    currentUserId,
    impersonatingUserId,
    handleImpersonateClick,
    onUserUpdated
  );

  if (users.length === 0 && !isLoading) {
    return (
      <div className="text-center p-8 bg-muted/20 rounded-lg">
        <p className="mb-4 text-muted-foreground">No users found</p>
        <Button
          variant="outline"
          onClick={handleRefresh}
          disabled={isRefreshingLocal}
          className="flex items-center gap-2"
        >
          {isRefreshingLocal ? (
            <>
              <RefreshCw className="h-4 w-4 animate-spin" />
              Refreshing...
            </>
          ) : (
            <>
              <RefreshCw className="h-4 w-4" />
              Refresh
            </>
          )}
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium">{users.length} User{users.length !== 1 ? 's' : ''}</h3>
        <Button
          variant="outline"
          onClick={handleRefresh}
          disabled={isRefreshingLocal}
          className="flex items-center gap-2"
        >
          {isRefreshingLocal ? (
            <>
              <RefreshCw className="h-4 w-4 animate-spin" />
              Refreshing...
            </>
          ) : (
            <>
              <RefreshCw className="h-4 w-4" />
              Refresh Users
            </>
          )}
        </Button>
      </div>

      <div className="rounded-md border overflow-hidden">
        <AdminTable
          columns={columns}
          data={users}
          isLoading={isLoading}
          emptyMessage="No users found"
          keyExtractor={(user) => user.id}
          testId="user-list-table"
        />
      </div>
    </div>
  );
};

export default UserList;
