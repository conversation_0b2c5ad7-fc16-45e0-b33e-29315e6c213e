import { PermissionType } from '@/types/auth';

export interface PermissionGuardProps {
  children: React.ReactNode;
  permission: PermissionType;
  teamId?: string;
  fallback?: React.ReactNode;
}

export interface RefreshButtonProps {
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  refreshAll?: boolean;
  className?: string;
  label?: string;
  queryKeys?: string[];
}

export interface SimplePaginationProps {
  currentPage: number;
  totalItems: number;
  itemsPerPage: number;
  onPageChange: (page: number) => void;
}

export interface PaginationConfig {
  maxPagesToShow: number;
  showEllipsis: boolean;
}

export interface PaginationCalculation {
  totalPages: number;
  pageNumbers: (number | string)[];
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

export interface RefreshStrategy {
  type: 'all' | 'route' | 'specific';
  queryKeys?: string[];
  routePath?: string;
}

export interface PermissionCheckResult {
  hasAccess: boolean;
  reason?: string;
}

export type PageNumberItem = number | 'ellipsis-start' | 'ellipsis-end';

export interface PaginationItemProps {
  page: PageNumberItem;
  currentPage: number;
  onPageChange: (page: number) => void;
  index: number;
}