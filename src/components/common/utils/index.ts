import { PermissionType } from '@/types/auth';
import { 
  PaginationCalculation, 
  PaginationConfig, 
  PageNumberItem, 
  PermissionCheckResult,
  RefreshStrategy 
} from '../types';
import { 
  PAGINATION_DEFAULTS, 
  PAGINATION_ELLIPSIS, 
  PERMISSION_ROLES,
  PERMISSION_CHECK_REASONS 
} from '../constants';

/**
 * Calculate pagination data including page numbers and navigation state
 */
export const calculatePagination = (
  currentPage: number,
  totalItems: number,
  itemsPerPage: number,
  config: Partial<PaginationConfig> = {}
): PaginationCalculation => {
  const { maxPagesToShow = PAGINATION_DEFAULTS.MAX_PAGES_TO_SHOW } = config;
  
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  
  if (totalPages <= 1) {
    return {
      totalPages,
      pageNumbers: [],
      hasNextPage: false,
      hasPreviousPage: false,
    };
  }

  const pageNumbers = calculatePageNumbers(currentPage, totalPages, maxPagesToShow);
  
  return {
    totalPages,
    pageNumbers,
    hasNextPage: currentPage < totalPages,
    hasPreviousPage: currentPage > 1,
  };
};

/**
 * Calculate which page numbers to display in pagination
 */
export const calculatePageNumbers = (
  currentPage: number,
  totalPages: number,
  maxPagesToShow: number
): PageNumberItem[] => {
  const pageNumbers: PageNumberItem[] = [];
  
  if (totalPages <= maxPagesToShow) {
    // Show all pages if there are few
    for (let i = 1; i <= totalPages; i++) {
      pageNumbers.push(i);
    }
  } else {
    // Show a subset of pages with current page in the middle
    let startPage = Math.max(1, currentPage - Math.floor(maxPagesToShow / 2));
    let endPage = startPage + maxPagesToShow - 1;
    
    if (endPage > totalPages) {
      endPage = totalPages;
      startPage = Math.max(1, endPage - maxPagesToShow + 1);
    }
    
    // Add first page and ellipsis if needed
    if (startPage > 1) {
      pageNumbers.push(1);
      if (startPage > 2) {
        pageNumbers.push(PAGINATION_ELLIPSIS.START);
      }
    }
    
    // Add middle pages
    for (let i = startPage; i <= endPage; i++) {
      pageNumbers.push(i);
    }
    
    // Add last page and ellipsis if needed
    if (endPage < totalPages) {
      if (endPage < totalPages - 1) {
        pageNumbers.push(PAGINATION_ELLIPSIS.END);
      }
      pageNumbers.push(totalPages);
    }
  }
  
  return pageNumbers;
};

/**
 * Check if a page number item is an ellipsis
 */
export const isEllipsis = (page: PageNumberItem): boolean => {
  return page === PAGINATION_ELLIPSIS.START || page === PAGINATION_ELLIPSIS.END;
};

/**
 * Create a safe page change handler that prevents invalid page numbers
 */
export const createPageChangeHandler = (
  onPageChange: (page: number) => void,
  totalPages: number
) => {
  return (page: number) => {
    if (page >= 1 && page <= totalPages) {
      onPageChange(page);
    }
  };
};

/**
 * Check if user has permission with detailed reasoning
 */
export const checkPermission = (
  permission: PermissionType,
  userRole: string | undefined,
  hasPermissionFn: (permission: PermissionType, teamId?: string) => boolean,
  isAdminFn: () => boolean,
  teamId?: string
): PermissionCheckResult => {
  // Admins and super admins can see everything
  if (isAdminFn()) {
    return {
      hasAccess: true,
      reason: PERMISSION_CHECK_REASONS.ADMIN_ACCESS,
    };
  }

  // Property managers can see everything
  if (userRole === PERMISSION_ROLES.PROPERTY_MANAGER) {
    return {
      hasAccess: true,
      reason: PERMISSION_CHECK_REASONS.PROPERTY_MANAGER_ACCESS,
    };
  }

  // Check if the user has the specific permission
  if (hasPermissionFn(permission, teamId)) {
    return {
      hasAccess: true,
      reason: PERMISSION_CHECK_REASONS.PERMISSION_GRANTED,
    };
  }

  // User doesn't have permission
  return {
    hasAccess: false,
    reason: PERMISSION_CHECK_REASONS.PERMISSION_DENIED,
  };
};

/**
 * Create refresh strategy based on parameters
 */
export const createRefreshStrategy = (
  refreshAll?: boolean,
  queryKeys?: string[],
  routePath?: string
): RefreshStrategy => {
  if (queryKeys && queryKeys.length > 0) {
    return {
      type: 'specific',
      queryKeys,
    };
  }
  
  if (refreshAll) {
    return {
      type: 'all',
    };
  }
  
  return {
    type: 'route',
    routePath,
  };
};

/**
 * Prevent default event behavior and execute callback
 */
export const createEventHandler = <T extends Event>(
  callback: () => void,
  shouldPreventDefault: boolean = true
) => {
  return (event: T) => {
    if (shouldPreventDefault) {
      event.preventDefault();
    }
    callback();
  };
};

/**
 * Get pagination info text (e.g., "Showing 1-10 of 100 items")
 */
export const getPaginationInfo = (
  currentPage: number,
  itemsPerPage: number,
  totalItems: number
): string => {
  if (totalItems === 0) {
    return 'No items found';
  }
  
  const startItem = (currentPage - 1) * itemsPerPage + 1;
  const endItem = Math.min(currentPage * itemsPerPage, totalItems);
  
  return `Showing ${startItem}-${endItem} of ${totalItems} items`;
};

/**
 * Validate pagination parameters
 */
export const validatePaginationParams = (
  currentPage: number,
  totalItems: number,
  itemsPerPage: number
): boolean => {
  return (
    currentPage > 0 &&
    totalItems >= 0 &&
    itemsPerPage > 0
  );
};