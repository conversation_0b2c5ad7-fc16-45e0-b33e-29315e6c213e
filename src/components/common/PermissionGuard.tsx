import React from 'react';
import { usePermissionCheck } from './hooks';
import { PermissionGuardProps } from './types';

/**
 * A component that conditionally renders its children based on user permissions.
 * 
 * @param children - The content to render if the user has the required permission
 * @param permission - The permission required to view the content
 * @param teamId - Optional team ID to check permission against
 * @param fallback - Optional content to render if the user doesn't have permission
 */
const PermissionGuard: React.FC<PermissionGuardProps> = ({
  children,
  permission,
  teamId,
  fallback = null
}) => {
  const { hasAccess } = usePermissionCheck({ permission, teamId });

  if (hasAccess) {
    return <>{children}</>;
  }

  // User doesn't have permission, render fallback or nothing
  return <>{fallback}</>;
};

export default PermissionGuard;
