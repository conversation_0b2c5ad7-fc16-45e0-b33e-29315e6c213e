import React from 'react';
import {
  PaginationItem,
  PaginationPrevious,
  PaginationNext,
} from '@/components/ui/pagination';
import { createEventHandler } from '../utils';

interface PaginationNavigationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  position: 'previous' | 'next';
}

export const PaginationNavigation: React.FC<PaginationNavigationProps> = ({
  currentPage,
  totalPages,
  onPageChange,
  hasNextPage,
  hasPreviousPage,
  position
}) => {
  const handlePrevious = createEventHandler(() => {
    if (hasPreviousPage) {
      onPageChange(currentPage - 1);
    }
  });

  const handleNext = createEventHandler(() => {
    if (hasNextPage) {
      onPageChange(currentPage + 1);
    }
  });

  if (position === 'previous') {
    return (
      <PaginationItem>
        <PaginationPrevious 
          href="#" 
          onClick={handlePrevious}
          className={!hasPreviousPage ? 'pointer-events-none opacity-50' : ''}
        />
      </PaginationItem>
    );
  }

  return (
    <PaginationItem>
      <PaginationNext 
        href="#" 
        onClick={handleNext}
        className={!hasNextPage ? 'pointer-events-none opacity-50' : ''}
      />
    </PaginationItem>
  );
};