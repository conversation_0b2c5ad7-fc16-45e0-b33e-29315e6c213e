import React from 'react';
import {
  PaginationItem as UIPaginationItem,
  PaginationLink,
  PaginationEllipsis,
} from '@/components/ui/pagination';
import { PaginationItemProps } from '../types';
import { isEllipsis, createEventHandler } from '../utils';

export const PaginationItem: React.FC<PaginationItemProps> = ({
  page,
  currentPage,
  onPageChange,
  index
}) => {
  if (isEllipsis(page)) {
    return (
      <UIPaginationItem key={`ellipsis-${index}`}>
        <PaginationEllipsis />
      </UIPaginationItem>
    );
  }

  const numericPage = page as number;
  const handleClick = createEventHandler(() => onPageChange(numericPage));

  return (
    <UIPaginationItem key={numericPage}>
      <PaginationLink 
        href="#" 
        isActive={currentPage === numericPage}
        onClick={handleClick}
      >
        {numericPage}
      </PaginationLink>
    </UIPaginationItem>
  );
};