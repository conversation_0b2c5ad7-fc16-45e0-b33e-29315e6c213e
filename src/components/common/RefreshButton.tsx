import React from 'react';
import { Button } from '@/components/ui/button';
import { Loader2, RefreshCw } from 'lucide-react';
import { useRefreshStrategies } from './hooks';
import { RefreshButtonProps } from './types';
import { REFRESH_BUTTON_DEFAULTS } from './constants';

/**
 * A simplified RefreshButton component that uses React Query's built-in functionality
 * to refresh data. This component can refresh all data or specific query keys.
 */
const RefreshButton: React.FC<RefreshButtonProps> = ({
  variant = REFRESH_BUTTON_DEFAULTS.VARIANT,
  size = REFRESH_BUTTON_DEFAULTS.SIZE,
  refreshAll = REFRESH_BUTTON_DEFAULTS.REFRESH_ALL,
  className = '',
  label = REFRESH_BUTTON_DEFAULTS.LABEL,
  queryKeys
}) => {
  const { isRefreshing, refreshWithCurrentStrategy } = useRefreshStrategies({
    refreshAll,
    queryKeys
  });

  return (
    <Button
      variant={variant}
      size={size}
      onClick={refreshWithCurrentStrategy}
      disabled={isRefreshing}
      className={className}
    >
      {isRefreshing ? (
        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
      ) : (
        <RefreshCw className="mr-2 h-4 w-4" />
      )}
      {label}
    </Button>
  );
};

export default RefreshButton;
