import React from 'react';
import { Pagination, PaginationContent } from '@/components/ui/pagination';
import { usePagination } from './hooks';
import { PaginationItem, PaginationNavigation } from './components';
import { SimplePaginationProps } from './types';

const SimplePagination: React.FC<SimplePaginationProps> = ({
  currentPage,
  totalItems,
  itemsPerPage,
  onPageChange
}) => {
  const {
    pageNumbers,
    hasNextPage,
    hasPreviousPage,
    totalPages,
    shouldShowPagination,
    createSafePageHandler
  } = usePagination({
    currentPage,
    totalItems,
    itemsPerPage
  });

  if (!shouldShowPagination) {
    return null;
  }

  const safePageChange = createSafePageHandler(onPageChange);

  return (
    <Pagination className="mt-4">
      <PaginationContent>
        <PaginationNavigation
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={safePageChange}
          hasNextPage={hasNextPage}
          hasPreviousPage={hasPreviousPage}
          position="previous"
        />
        
        {pageNumbers.map((page, index) => (
          <PaginationItem
            key={typeof page === 'number' ? page : `ellipsis-${index}`}
            page={page}
            currentPage={currentPage}
            onPageChange={safePageChange}
            index={index}
          />
        ))}

        <PaginationNavigation
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={safePageChange}
          hasNextPage={hasNextPage}
          hasPreviousPage={hasPreviousPage}
          position="next"
        />
      </PaginationContent>
    </Pagination>
  );
};

export default SimplePagination;
