import { useMemo } from 'react';
import { PermissionType } from '@/types/auth';
import { usePermissions } from '@/hooks/usePermissions';
import { useAuth } from '@/contexts/AuthContext';
import { checkPermission } from '../utils';

export interface UsePermissionCheckProps {
  permission: PermissionType;
  teamId?: string;
}

export const usePermissionCheck = ({ permission, teamId }: UsePermissionCheckProps) => {
  const { hasPermission, isAdmin } = usePermissions();
  const { authState } = useAuth();

  const permissionResult = useMemo(() => {
    return checkPermission(
      permission,
      authState?.profile?.role,
      hasPermission,
      isAdmin,
      teamId
    );
  }, [permission, authState?.profile?.role, hasPermission, isAdmin, teamId]);

  return {
    hasAccess: permissionResult.hasAccess,
    reason: permissionResult.reason,
    userRole: authState?.profile?.role,
    isAuthenticated: !!authState?.user,
  };
};