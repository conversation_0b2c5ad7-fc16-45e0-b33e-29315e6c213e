import { useMemo } from 'react';
import { calculatePagination, createPageChangeHandler, validatePaginationParams } from '../utils';
import { PaginationConfig } from '../types';
import { PAGINATION_DEFAULTS } from '../constants';

export interface UsePaginationProps {
  currentPage: number;
  totalItems: number;
  itemsPerPage?: number;
  config?: Partial<PaginationConfig>;
}

export const usePagination = ({
  currentPage,
  totalItems,
  itemsPerPage = PAGINATION_DEFAULTS.ITEMS_PER_PAGE,
  config = {}
}: UsePaginationProps) => {
  
  const isValid = useMemo(() => 
    validatePaginationParams(currentPage, totalItems, itemsPerPage),
    [currentPage, totalItems, itemsPerPage]
  );

  const paginationData = useMemo(() => {
    if (!isValid) {
      return {
        totalPages: 0,
        pageNumbers: [],
        hasNextPage: false,
        hasPreviousPage: false,
      };
    }
    
    return calculatePagination(currentPage, totalItems, itemsPerPage, config);
  }, [currentPage, totalItems, itemsPerPage, config, isValid]);

  const createSafePageHandler = (onPageChange: (page: number) => void) => {
    return createPageChangeHandler(onPageChange, paginationData.totalPages);
  };

  const shouldShowPagination = paginationData.totalPages > 1;

  return {
    ...paginationData,
    isValid,
    shouldShowPagination,
    createSafePageHandler,
  };
};