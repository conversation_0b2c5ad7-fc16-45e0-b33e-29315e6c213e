import { useCallback } from 'react';
import { useLocation } from 'react-router-dom';
import { useQueryClient } from '@tanstack/react-query';
import { useGlobalDataRefresh } from '@/contexts/GlobalDataRefreshContext';
import { createRefreshStrategy } from '../utils';
import { RefreshStrategy } from '../types';

export interface UseRefreshStrategiesProps {
  refreshAll?: boolean;
  queryKeys?: string[];
}

export const useRefreshStrategies = ({ 
  refreshAll = false, 
  queryKeys 
}: UseRefreshStrategiesProps = {}) => {
  const { refreshAllData, refreshRouteData, isRefreshing } = useGlobalDataRefresh();
  const location = useLocation();
  const queryClient = useQueryClient();

  const executeRefreshStrategy = useCallback(async (strategy: RefreshStrategy) => {
    switch (strategy.type) {
      case 'specific':
        if (strategy.queryKeys && strategy.queryKeys.length > 0) {
          console.log(`[RefreshStrategies] Refreshing specific query keys: ${strategy.queryKeys.join(', ')}`);
          
          for (const key of strategy.queryKeys) {
            await queryClient.invalidateQueries({ queryKey: [key], exact: false });
            await queryClient.refetchQueries({ queryKey: [key], exact: false, type: 'all' });
          }
        }
        break;
        
      case 'all':
        await refreshAllData();
        break;
        
      case 'route':
        await refreshRouteData(strategy.routePath || location.pathname);
        break;
        
      default:
        console.warn('[RefreshStrategies] Unknown refresh strategy type:', strategy);
    }
  }, [queryClient, refreshAllData, refreshRouteData, location.pathname]);

  const refreshWithCurrentStrategy = useCallback(async () => {
    if (isRefreshing) return;

    const strategy = createRefreshStrategy(refreshAll, queryKeys, location.pathname);
    await executeRefreshStrategy(strategy);
  }, [isRefreshing, refreshAll, queryKeys, location.pathname, executeRefreshStrategy]);

  const refreshSpecific = useCallback(async (keys: string[]) => {
    if (isRefreshing) return;

    const strategy = createRefreshStrategy(false, keys);
    await executeRefreshStrategy(strategy);
  }, [isRefreshing, executeRefreshStrategy]);

  const refreshEverything = useCallback(async () => {
    if (isRefreshing) return;

    const strategy = createRefreshStrategy(true);
    await executeRefreshStrategy(strategy);
  }, [isRefreshing, executeRefreshStrategy]);

  const refreshRoute = useCallback(async (routePath?: string) => {
    if (isRefreshing) return;

    const strategy = createRefreshStrategy(false, undefined, routePath || location.pathname);
    await executeRefreshStrategy(strategy);
  }, [isRefreshing, location.pathname, executeRefreshStrategy]);

  return {
    isRefreshing,
    refreshWithCurrentStrategy,
    refreshSpecific,
    refreshEverything,
    refreshRoute,
    executeRefreshStrategy,
  };
};