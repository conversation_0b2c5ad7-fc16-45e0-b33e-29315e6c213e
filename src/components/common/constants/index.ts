export const PAGINATION_DEFAULTS = {
  MAX_PAGES_TO_SHOW: 5,
  SHOW_ELLIPSIS: true,
  ITEMS_PER_PAGE: 10,
} as const;

export const REFRESH_BUTTON_DEFAULTS = {
  VARIANT: 'outline' as const,
  SIZE: 'sm' as const,
  LABEL: 'Refresh',
  REFRESH_ALL: false,
} as const;

export const PERMISSION_ROLES = {
  ADMIN: 'admin',
  SUPER_ADMIN: 'super_admin',
  PROPERTY_MANAGER: 'property_manager',
  SERVICE_PROVIDER: 'service_provider',
} as const;

export const PAGINATION_ELLIPSIS = {
  START: 'ellipsis-start',
  END: 'ellipsis-end',
} as const;

export const REFRESH_STRATEGIES = {
  ALL: 'all',
  ROUTE: 'route', 
  SPECIFIC: 'specific',
} as const;

export const PERMISSION_CHECK_REASONS = {
  ADMIN_ACCESS: 'Ad<PERSON> has full access',
  PROPERTY_MANAGER_ACCESS: 'Property manager has full access',
  PERMISSION_GRANTED: 'User has required permission',
  PERMISSION_DENIED: 'User lacks required permission',
  NOT_AUTHENTICATED: 'User is not authenticated',
} as const;