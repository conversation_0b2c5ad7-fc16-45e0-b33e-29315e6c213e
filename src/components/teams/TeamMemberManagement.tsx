
import { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { UserRole } from '@/types/supabase';
import InviteTeamMemberDialog from './InviteTeamMemberDialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { UserPlus, Trash2, Loader2, Users } from 'lucide-react';
import { toast } from 'sonner';
import { useTeamData } from './hooks/useTeamData';

interface TeamMemberManagementProps {
  teamId: string;
}

const TeamMemberManagement: React.FC<TeamMemberManagementProps> = ({ teamId }) => {
  const {
    teamMembers,
    loading,
    isProcessing,
    canInviteMembers,
    isTeamOwner,
    isAdmin,
    isProper<PERSON><PERSON>anager,
    inviteUserToTeam,
    removeTeamMember,
    fetchTeamMembers,
    setIsProcessing,
    authState,
  } = useTeamData(teamId);

  const [inviteDialogOpen, setInviteDialogOpen] = useState(false);

  const handleInviteUser = async (email: string, role: UserRole) => {
    if (isProcessing) return;

    setIsProcessing(true);
    try {
      await inviteUserToTeam(teamId, email, role);
      toast.success(`Invitation sent to ${email}`);
      setInviteDialogOpen(false);
    } catch (error) {
      console.error('Error inviting user:', error);
      toast.error('Failed to send invitation');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleRemoveMember = async (memberId: string) => {
    if (isProcessing) return;

    setIsProcessing(true);
    try {
      const success = await removeTeamMember(memberId);
      if (success) {
        await fetchTeamMembers(teamId);
        toast.success('Team member removed successfully');
      }
    } catch (error) {
      console.error('Error removing team member:', error);
      toast.error('Failed to remove team member');
    } finally {
      setIsProcessing(false);
    }
  };

  const getInitials = (firstName?: string, lastName?: string) => {
    const first = firstName?.charAt(0) || '';
    const last = lastName?.charAt(0) || '';
    return (first + last).toUpperCase() || 'U';
  };

  return (
    <Card>
      <CardContent className="p-0">
        <div className="p-4 sm:p-6 flex justify-between items-center">
          <h3 className="text-lg font-medium">Team Members</h3>
          {canInviteMembers && (
            <Button
              onClick={() => setInviteDialogOpen(true)}
              className="flex items-center gap-2"
              disabled={isProcessing}
            >
              <UserPlus className="h-4 w-4" />
              <span>Invite</span>
            </Button>
          )}
        </div>

        {loading ? (
          <div className="flex justify-center items-center p-8">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        ) : teamMembers.length === 0 ? (
          <div className="text-center p-8 border-t">
            <Users className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <p className="text-muted-foreground mb-4">No team members yet</p>
            {canInviteMembers && (
              <Button
                onClick={() => setInviteDialogOpen(true)}
                variant="outline"
                disabled={isProcessing}
              >
                <UserPlus className="h-4 w-4 mr-2" />
                Invite Members
              </Button>
            )}
          </div>
        ) : (
          <div className="overflow-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>User</TableHead>
                  <TableHead>Role</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {teamMembers.map((member) => (
                  <TableRow key={member.id}>
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <Avatar className="h-8 w-8">
                          <AvatarImage src={member.avatar_url || undefined} />
                          <AvatarFallback>
                            {getInitials(member.first_name, member.last_name)}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-medium">
                            {member.first_name || member.last_name
                              ? `${member.first_name || ''} ${member.last_name || ''}`.trim()
                              : 'Unnamed User'}
                          </div>
                          <div className="text-xs text-muted-foreground">{member.email}</div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        {member.profile_role === 'property_manager'
                          ? 'Property Manager'
                          : member.profile_role === 'service_provider'
                            ? 'Service Provider'
                            : member.profile_role || 'User'}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        <span className={`h-2 w-2 rounded-full mr-2 ${member.status === 'active' ? 'bg-green-500' : 'bg-amber-500'}`}></span>
                        <span className="capitalize">{member.status}</span>
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      {canInviteMembers && member.user_id !== authState.user?.id && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleRemoveMember(member.id)}
                          className="text-destructive hover:text-destructive/90 hover:bg-destructive/10"
                          disabled={isProcessing}
                        >
                          <Trash2 className="h-4 w-4" />
                          <span className="sr-only">Remove</span>
                        </Button>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>

      <InviteTeamMemberDialog
        open={inviteDialogOpen}
        onOpenChange={setInviteDialogOpen}
        onInvite={handleInviteUser}
        canInviteStaff={isTeamOwner || isAdmin || isPropertyManager || canInviteMembers}
        canInviteProviders={isTeamOwner || isAdmin || isPropertyManager || canInviteMembers}
        isTeamOwner={isTeamOwner}
        isAdmin={isAdmin}
        isPropertyManager={isPropertyManager}
        isLoading={isProcessing}
      />
    </Card>
  );
};

export default TeamMemberManagement;
