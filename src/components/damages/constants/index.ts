import { SelectOption, DamageStatus, SeverityLevel, Platform, InvoiceStatus } from '../types';

// Status Options
export const STATUS_OPTIONS: SelectOption<DamageStatus>[] = [
  { value: 'open', label: 'Open' },
  { value: 'pending', label: 'Pending' },
  { value: 'in_progress', label: 'In Progress' },
  { value: 'resolved', label: 'Resolved' },
  { value: 'closed', label: 'Closed' },
];

// Severity Options
export const SEVERITY_OPTIONS: SelectOption<SeverityLevel>[] = [
  { value: 'low', label: 'Low' },
  { value: 'medium', label: 'Medium' },
  { value: 'high', label: 'High' },
  { value: 'critical', label: 'Critical' },
];

// Platform Options
export const PLATFORM_OPTIONS: SelectOption<Platform>[] = [
  { value: 'Airbnb', label: 'Airbnb' },
  { value: 'VRBO', label: 'VRBO' },
  { value: 'Booking.com', label: 'Booking.com' },
  { value: 'Expedia', label: 'Expedia' },
  { value: 'Other', label: 'Other' },
];

// Invoice Status Options
export const INVOICE_STATUS_OPTIONS: SelectOption<InvoiceStatus>[] = [
  { value: 'draft', label: 'Draft' },
  { value: 'sent', label: 'Sent' },
  { value: 'paid', label: 'Paid' },
  { value: 'overdue', label: 'Overdue' },
  { value: 'cancelled', label: 'Cancelled' },
];

// Status Colors
export const STATUS_COLORS = {
  open: 'bg-blue-500',
  pending: 'bg-yellow-500',
  in_progress: 'bg-orange-500',
  resolved: 'bg-green-500',
  closed: 'bg-gray-500',
} as const;

// Severity Colors
export const SEVERITY_COLORS = {
  low: 'bg-green-500 dark:bg-green-600',
  medium: 'bg-yellow-500 dark:bg-yellow-600',
  high: 'bg-orange-500 dark:bg-orange-600',
  critical: 'bg-red-500 dark:bg-red-600',
} as const;

// Invoice Status Colors
export const INVOICE_STATUS_COLORS = {
  draft: 'bg-gray-500',
  sent: 'bg-blue-500',
  paid: 'bg-green-500',
  overdue: 'bg-red-500',
  cancelled: 'bg-gray-400',
} as const;

// Form Defaults
export const DAMAGE_REPORT_DEFAULTS = {
  status: 'open' as DamageStatus,
  severity: 'medium' as SeverityLevel,
  estimated_cost: 0,
  incident_date: new Date().toISOString().split('T')[0],
} as const;

// File Upload Constraints
export const PHOTO_UPLOAD_CONSTRAINTS = {
  MAX_FILE_SIZE: 5 * 1024 * 1024, // 5MB
  ACCEPTED_TYPES: ['image/jpeg', 'image/png', 'image/webp'],
  MAX_FILES_PER_UPLOAD: 10,
} as const;

// Validation Messages
export const VALIDATION_MESSAGES = {
  REQUIRED_FIELD: 'This field is required',
  INVALID_EMAIL: 'Please enter a valid email address',
  INVALID_AMOUNT: 'Please enter a valid amount',
  INVALID_DATE: 'Please enter a valid date',
  FILE_TOO_LARGE: `File size must be less than ${PHOTO_UPLOAD_CONSTRAINTS.MAX_FILE_SIZE / (1024 * 1024)}MB`,
  INVALID_FILE_TYPE: 'Only JPEG, PNG, and WebP images are allowed',
  TOO_MANY_FILES: `Maximum ${PHOTO_UPLOAD_CONSTRAINTS.MAX_FILES_PER_UPLOAD} files allowed`,
} as const;

// Toast Messages
export const TOAST_MESSAGES = {
  REPORT_CREATED: 'Damage report created successfully',
  REPORT_UPDATED: 'Damage report updated successfully',
  REPORT_DELETED: 'Damage report deleted successfully',
  PHOTOS_UPLOADED: 'Photos uploaded successfully',
  PHOTO_DELETED: 'Photo deleted successfully',
  INVOICE_CREATED: 'Invoice created successfully',
  INVOICE_UPDATED: 'Invoice updated successfully',
  INVOICE_DELETED: 'Invoice deleted successfully',
  NOTE_ADDED: 'Note added successfully',
  ERROR_GENERIC: 'An error occurred. Please try again.',
  ERROR_UPLOAD: 'Failed to upload files. Please try again.',
  ERROR_DELETE: 'Failed to delete. Please try again.',
  CONFIRM_DELETE_REPORT: 'Are you sure you want to delete this damage report?',
  CONFIRM_DELETE_PHOTO: 'Are you sure you want to delete this photo?',
  CONFIRM_DELETE_INVOICE: 'Are you sure you want to delete this invoice?',
} as const;

// Activity Actions
export const ACTIVITY_ACTIONS = {
  CREATED: 'created',
  UPDATED: 'updated',
  STATUS_CHANGED: 'status_changed',
  PHOTO_ADDED: 'photo_added',
  PHOTO_REMOVED: 'photo_removed',
  INVOICE_ADDED: 'invoice_added',
  INVOICE_UPDATED: 'invoice_updated',
  NOTE_ADDED: 'note_added',
} as const;

// Export Options
export const EXPORT_FORMATS = {
  PDF: 'pdf',
  CSV: 'csv',
  EXCEL: 'excel',
} as const;

// Date Formats
export const DATE_FORMATS = {
  DISPLAY: 'MMM dd, yyyy',
  INPUT: 'yyyy-MM-dd',
  FULL: 'MMM dd, yyyy HH:mm',
} as const;