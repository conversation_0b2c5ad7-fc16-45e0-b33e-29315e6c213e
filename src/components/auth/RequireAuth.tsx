import React, { useEffect, useState, useRef } from 'react';
import { Navigate, useLocation, Outlet } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { useSessionValidation, useSessionStorage, useTimerCleanup, useAuthRedirect } from './hooks';
import { DEFAULT_TIMEOUTS } from './constants';

export const RequireAuth = () => {
  const { authState, refreshProfile } = useAuth();
  const location = useLocation();
  const [isCheckingSession, setIsCheckingSession] = useState(true);
  const [hasSession, setHasSession] = useState<boolean | null>(null);
  
  const { validateSession, refreshSession } = useSessionValidation();
  const { getSession, saveCurrentSession, handleBeforeUnload, isPageRefreshed } = useSessionStorage();
  const { setInterval, clearAllTimers } = useTimerCleanup();
  const { redirectToLogin: performRedirectToLogin } = useAuthRedirect();

  // Use refs to track session check state
  const sessionCheckedRef = useRef(false);
  const refreshAttemptedRef = useRef(false);
  const lastSessionCheckRef = useRef(0);


  // Simplified checkSession function using extracted hooks
  const checkSession = async (force = false) => {
    const now = Date.now();
    if (!force && sessionCheckedRef.current && (now - lastSessionCheckRef.current < 10000)) {
      return;
    }

    lastSessionCheckRef.current = now;
    setIsCheckingSession(true);

    try {
      // Check if we already have a user in auth state
      if (authState?.user && authState?.isAuthenticated) {
        if (authState.session) {
          saveCurrentSession();
        }
        setHasSession(true);
        setIsCheckingSession(false);
        sessionCheckedRef.current = true;
        return;
      }

      // Use extracted session validation
      const result = await validateSession();
      
      if (result.isValid) {
        setHasSession(true);
        setIsCheckingSession(false);
        sessionCheckedRef.current = true;
        return;
      }

      if (result.needsRefresh && !refreshAttemptedRef.current) {
        refreshAttemptedRef.current = true;
        const refreshed = await refreshSession();
        
        if (refreshed) {
          await refreshProfile();
          setHasSession(true);
          setIsCheckingSession(false);
          sessionCheckedRef.current = true;
          return;
        }
      }

      // No valid session found
      setHasSession(false);
      setIsCheckingSession(false);
      await performRedirectToLogin({ reason: 'session_expired' });
    } catch (error) {
      console.error('RequireAuth: Error in checkSession:', error);
      setHasSession(false);
      setIsCheckingSession(false);
      await performRedirectToLogin({ reason: 'session_expired' });
    }
  };

  // Handle initial session check and page refresh
  useEffect(() => {
    if (isPageRefreshed()) {
      console.log('RequireAuth: Page was refreshed, checking session immediately');
      checkSession(true);
    } else {
      checkSession();
    }
  }, []);

  // Set up beforeunload listener
  useEffect(() => {
    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [handleBeforeUnload]);

  // Set up periodic session checking
  useEffect(() => {
    const intervalId = window.setInterval(() => {
      checkSession();
    }, DEFAULT_TIMEOUTS.SESSION_CHECK_INTERVAL);

    setInterval('sessionCheck', intervalId);

    return () => {
      clearAllTimers();
    };
  }, []);

  // Show loading state while checking authentication
  if (authState?.isLoading || isCheckingSession) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary opacity-70"></div>
      </div>
    );
  }

  // Check if session is valid
  if (hasSession === false) {
    return <Navigate to="/#/login" state={{ reason: 'session_expired' }} replace />;
  }

  // User is authenticated, render the protected route
  console.log('RequireAuth: User authenticated, rendering protected content');
  return <Outlet />;
};

export default RequireAuth;
