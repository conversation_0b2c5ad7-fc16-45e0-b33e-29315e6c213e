import { STORAGE_KEYS, SESSION_REFRESH } from '../constants';
import { SessionStorageData, SessionValidationResult } from '../types';

/**
 * Check if a session is valid based on expiry time
 */
export const isSessionValid = (session: any): boolean => {
  if (!session) return false;

  // If no expires_at, assume it's valid (safer approach)
  if (!session.expires_at) return true;

  const now = Math.floor(Date.now() / 1000);
  
  // Consider valid even if expired but within grace period
  const gracePeriod = SESSION_REFRESH.GRACE_PERIOD;
  return (session.expires_at + gracePeriod) > now;
};

/**
 * Get session from localStorage with fallback checks
 */
export const getStoredSession = (): any => {
  try {
    // First try our custom storage key
    const localSession = localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);
    if (localSession) {
      try {
        const parsedSession = JSON.parse(localSession);
        if (parsedSession?.user) {
          return parsedSession;
        }
      } catch (parseError) {
        console.error('Error parsing auth token:', parseError);
      }
    }

    // Then try sessionStorage
    const sessionStorageSession = sessionStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);
    if (sessionStorageSession) {
      try {
        const parsedSession = JSON.parse(sessionStorageSession);
        if (parsedSession?.user) {
          return parsedSession;
        }
      } catch (parseError) {
        console.error('Error parsing sessionStorage session:', parseError);
      }
    }

    // Also check the default Supabase storage location
    const supabaseSession = localStorage.getItem(STORAGE_KEYS.SUPABASE_TOKEN);
    if (supabaseSession) {
      try {
        const parsedSupabaseSession = JSON.parse(supabaseSession);
        if (parsedSupabaseSession?.currentSession?.user) {
          return parsedSupabaseSession.currentSession;
        }
      } catch (parseError) {
        console.error('Error parsing supabase session:', parseError);
      }
    }

    return null;
  } catch (e) {
    console.error('Error accessing storage:', e);
    return null;
  }
};

/**
 * Save session to storage with redundancy
 */
export const saveSessionToStorage = (session: any): void => {
  if (!session) return;

  try {
    const sessionData: SessionStorageData = {
      session,
      user: session.user,
      expiresAt: session.expires_at,
      timestamp: Date.now(),
    };

    const serializedData = JSON.stringify(sessionData.session);

    // Save to both localStorage and sessionStorage for redundancy
    localStorage.setItem(STORAGE_KEYS.AUTH_TOKEN, serializedData);
    sessionStorage.setItem(STORAGE_KEYS.AUTH_TOKEN, serializedData);

    // Save metadata
    localStorage.setItem(STORAGE_KEYS.HAS_SESSION, 'true');

    // Save expiry time for debugging
    if (session.expires_at) {
      const expiresAt = new Date(session.expires_at * 1000).toISOString();
      localStorage.setItem(STORAGE_KEYS.SESSION_EXPIRES, expiresAt);
    }
  } catch (e) {
    console.error('Error saving session to storage:', e);
  }
};

/**
 * Clear all session-related storage
 */
export const clearSessionStorage = (): void => {
  try {
    localStorage.removeItem(STORAGE_KEYS.AUTH_TOKEN);
    localStorage.removeItem(STORAGE_KEYS.SUPABASE_TOKEN);
    localStorage.removeItem(STORAGE_KEYS.HAS_SESSION);
    localStorage.removeItem(STORAGE_KEYS.SESSION_EXPIRES);
    sessionStorage.removeItem(STORAGE_KEYS.AUTH_TOKEN);
  } catch (e) {
    console.error('Error clearing session storage:', e);
  }
};

/**
 * Get current path for redirect after login
 */
export const getCurrentPath = (): string => {
  return window.location.hash.replace('#', '') || '/dashboard';
};

/**
 * Store redirect path for after login
 */
export const storeRedirectPath = (path?: string): void => {
  const redirectPath = path || getCurrentPath();
  sessionStorage.setItem(STORAGE_KEYS.REDIRECT_AFTER_LOGIN, redirectPath);
};

/**
 * Clear redirect path
 */
export const clearRedirectPath = (): void => {
  sessionStorage.removeItem(STORAGE_KEYS.REDIRECT_AFTER_LOGIN);
};

/**
 * Get stored redirect path
 */
export const getRedirectPath = (): string | null => {
  return sessionStorage.getItem(STORAGE_KEYS.REDIRECT_AFTER_LOGIN);
};

/**
 * Check if user is online
 */
export const isOnline = (): boolean => {
  return typeof navigator !== 'undefined' && 'onLine' in navigator 
    ? navigator.onLine 
    : true; // Assume online if can't detect
};

/**
 * Format time remaining for display
 */
export const formatTimeRemaining = (seconds: number): string => {
  if (seconds <= 0) return '0 seconds';
  
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  
  if (minutes > 0) {
    return `${minutes} minute${minutes !== 1 ? 's' : ''} ${remainingSeconds} second${remainingSeconds !== 1 ? 's' : ''}`;
  }
  
  return `${remainingSeconds} second${remainingSeconds !== 1 ? 's' : ''}`;
};

/**
 * Create a delay function for retry logic
 */
export const createDelay = (ms: number): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

/**
 * Dispatch custom event
 */
export const dispatchCustomEvent = (eventName: string, detail?: any): void => {
  try {
    window.dispatchEvent(new CustomEvent(eventName, { detail }));
  } catch (error) {
    console.error(`Error dispatching event ${eventName}:`, error);
  }
};