import { useRef, useCallback, useEffect } from 'react';
import { TimerRefs } from '../types';

export const useTimerCleanup = () => {
  const timerRefs = useRef<{
    [key: string]: NodeJS.Timeout | null;
  }>({});

  const setTimer = useCallback((name: string, timer: NodeJS.Timeout) => {
    // Clear existing timer if it exists
    if (timerRefs.current[name]) {
      clearTimeout(timerRefs.current[name]!);
    }
    timerRefs.current[name] = timer;
  }, []);

  const setInterval = useCallback((name: string, interval: NodeJS.Timeout) => {
    // Clear existing interval if it exists
    if (timerRefs.current[name]) {
      clearInterval(timerRefs.current[name]!);
    }
    timerRefs.current[name] = interval;
  }, []);

  const clearTimer = useCallback((name: string) => {
    if (timerRefs.current[name]) {
      clearTimeout(timerRefs.current[name]!);
      timerRefs.current[name] = null;
    }
  }, []);

  const clearAllTimers = useCallback(() => {
    Object.keys(timerRefs.current).forEach(name => {
      if (timerRefs.current[name]) {
        clearTimeout(timerRefs.current[name]!);
        timerRefs.current[name] = null;
      }
    });
  }, []);

  const getTimer = useCallback((name: string) => {
    return timerRefs.current[name];
  }, []);

  // Cleanup all timers on unmount
  useEffect(() => {
    return () => {
      clearAllTimers();
    };
  }, [clearAllTimers]);

  return {
    setTimer,
    setInterval,
    clearTimer,
    clearAllTimers,
    getTimer,
  };
};