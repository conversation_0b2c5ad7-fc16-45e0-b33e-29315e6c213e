import { useState, useEffect, useCallback } from 'react';
import { isOnline, dispatchCustomEvent } from '../utils';
import { CUSTOM_EVENTS } from '../constants';
import { NetworkStatus } from '../types';

export const useNetworkStatus = () => {
  const [networkStatus, setNetworkStatus] = useState<NetworkStatus>({
    isOnline: isOnline(),
    lastChecked: Date.now(),
  });

  const updateNetworkStatus = useCallback((online: boolean) => {
    const newStatus = {
      isOnline: online,
      lastChecked: Date.now(),
    };
    
    setNetworkStatus(newStatus);
    
    // Dispatch custom event for other components to listen
    dispatchCustomEvent(CUSTOM_EVENTS.NETWORK_STATUS, {
      online,
      timestamp: new Date().toISOString(),
    });
  }, []);

  const handleOnline = useCallback(() => {
    updateNetworkStatus(true);
  }, [updateNetworkStatus]);

  const handleOffline = useCallback(() => {
    updateNetworkStatus(false);
  }, [updateNetworkStatus]);

  useEffect(() => {
    // Add event listeners for online/offline events
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Initial check
    updateNetworkStatus(isOnline());

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [handleOnline, handleOffline, updateNetworkStatus]);

  return {
    isOnline: networkStatus.isOnline,
    lastChecked: networkStatus.lastChecked,
    networkStatus,
  };
};