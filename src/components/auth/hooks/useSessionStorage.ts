import { useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { 
  saveSessionToStorage, 
  clearSessionStorage, 
  getStoredSession,
  storeRedirectPath,
  getRedirectPath,
  clearRedirectPath 
} from '../utils';
import { STORAGE_KEYS } from '../constants';

export const useSessionStorage = () => {
  const { authState } = useAuth();

  const saveCurrentSession = useCallback(() => {
    if (authState?.session) {
      saveSessionToStorage(authState.session);
    }
  }, [authState?.session]);

  const clearSession = useCallback(() => {
    clearSessionStorage();
  }, []);

  const getSession = useCallback(() => {
    return getStoredSession();
  }, []);

  const handleBeforeUnload = useCallback(() => {
    if (authState?.session && authState?.user) {
      try {
        saveSessionToStorage(authState.session);
        sessionStorage.setItem(STORAGE_KEYS.PAGE_REFRESHED, 'true');
      } catch (e) {
        console.error('Error saving session before unload:', e);
      }
    }
  }, [authState?.session, authState?.user]);

  const isPageRefreshed = useCallback(() => {
    const wasRefreshed = sessionStorage.getItem(STORAGE_KEYS.PAGE_REFRESHED) === 'true';
    if (wasRefreshed) {
      sessionStorage.removeItem(STORAGE_KEYS.PAGE_REFRESHED);
    }
    return wasRefreshed;
  }, []);

  const saveRedirectPath = useCallback((path?: string) => {
    storeRedirectPath(path);
  }, []);

  const getSavedRedirectPath = useCallback(() => {
    return getRedirectPath();
  }, []);

  const clearSavedRedirectPath = useCallback(() => {
    clearRedirectPath();
  }, []);

  return {
    saveCurrentSession,
    clearSession,
    getSession,
    handleBeforeUnload,
    isPageRefreshed,
    saveRedirectPath,
    getSavedRedirectPath,
    clearSavedRedirectPath,
  };
};