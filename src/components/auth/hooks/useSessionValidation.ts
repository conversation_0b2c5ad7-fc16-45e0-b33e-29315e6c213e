import { useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { checkSession, refreshAuthToken } from '@/utils/sessionUtils';
import { isSessionValid, getStoredSession } from '../utils';
import { SessionValidationResult } from '../types';

export const useSessionValidation = () => {
  const { authState } = useAuth();

  const validateSession = useCallback(async (): Promise<SessionValidationResult> => {
    try {
      // First check if we have a user in auth state
      if (authState?.user && authState?.isAuthenticated) {
        // Quick validation with stored session
        const storedSession = getStoredSession();
        if (storedSession && isSessionValid(storedSession)) {
          return { isValid: true, needsRefresh: false };
        }
      }

      // Check session validity using sessionUtils
      const isValid = await checkSession();
      
      if (!isValid) {
        // Check if we can refresh
        const storedSession = getStoredSession();
        if (storedSession) {
          return { isValid: false, needsRefresh: true };
        }
        
        return { isValid: false, needsRefresh: false, error: 'No valid session found' };
      }

      return { isValid: true, needsRefresh: false };
    } catch (error: any) {
      return { 
        isValid: false, 
        needsRefresh: false, 
        error: error.message || 'Session validation failed' 
      };
    }
  }, [authState?.user, authState?.isAuthenticated]);

  const refreshSession = useCallback(async (): Promise<boolean> => {
    try {
      return await refreshAuthToken();
    } catch (error) {
      console.error('Error refreshing session:', error);
      return false;
    }
  }, []);

  return {
    validateSession,
    refreshSession,
    isAuthenticated: authState?.isAuthenticated || false,
    user: authState?.user,
  };
};