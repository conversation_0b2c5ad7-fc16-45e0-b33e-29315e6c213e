import { useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'sonner';
import { useAuth } from '@/contexts/AuthContext';
import { useSessionStorage } from './useSessionStorage';
import { AuthRedirectOptions } from '../types';
import { ROUTES, STORAGE_KEYS } from '../constants';

export const useAuthRedirect = () => {
  const navigate = useNavigate();
  const { signOut } = useAuth();
  const { clearSession, saveRedirectPath } = useSessionStorage();

  const redirectToLogin = useCallback(async (options: AuthRedirectOptions = {}) => {
    const {
      reason = 'not_authenticated',
      returnUrl,
      showToast = true,
      toastMessage,
    } = options;

    try {
      // Save current path for redirect after login
      saveRedirectPath(returnUrl);

      // Set session expired flag if needed
      if (reason === 'session_expired' || reason === 'idle_timeout') {
        sessionStorage.setItem(STORAGE_KEYS.SESSION_EXPIRED, 'true');
      }

      // Clear session data
      clearSession();

      // Sign out through auth context
      await signOut();

      // Show appropriate toast message
      if (showToast) {
        const message = toastMessage || getDefaultToastMessage(reason);
        if (reason === 'session_expired' || reason === 'idle_timeout') {
          toast.error(message);
        } else {
          toast.info(message);
        }
      }

      // Navigate to login with reason
      const loginUrl = `${ROUTES.LOGIN}?reason=${reason}`;
      navigate(loginUrl);
    } catch (error) {
      console.error('Error during auth redirect:', error);
      
      // Force redirect even if signOut fails
      clearSession();
      window.location.href = `${ROUTES.LOGIN}?reason=${reason}`;
    }
  }, [navigate, signOut, clearSession, saveRedirectPath]);

  const redirectToDashboard = useCallback(() => {
    navigate(ROUTES.DASHBOARD);
  }, [navigate]);

  const handleSessionExpired = useCallback(async () => {
    await redirectToLogin({
      reason: 'session_expired',
      showToast: true,
      toastMessage: 'Your session has expired. Please log in again to continue.',
    });
  }, [redirectToLogin]);

  const handleIdleTimeout = useCallback(async () => {
    await redirectToLogin({
      reason: 'idle_timeout',
      showToast: true,
      toastMessage: 'You have been logged out due to inactivity. Please log in again to continue.',
    });
  }, [redirectToLogin]);

  const handlePermissionDenied = useCallback(async () => {
    await redirectToLogin({
      reason: 'permission_denied',
      showToast: true,
      toastMessage: 'You do not have permission to access this resource.',
    });
  }, [redirectToLogin]);

  return {
    redirectToLogin,
    redirectToDashboard,
    handleSessionExpired,
    handleIdleTimeout,
    handlePermissionDenied,
  };
};

function getDefaultToastMessage(reason: string): string {
  switch (reason) {
    case 'session_expired':
      return 'Your session has expired. Please log in again.';
    case 'idle_timeout':
      return 'You have been logged out due to inactivity.';
    case 'permission_denied':
      return 'You do not have permission to access this resource.';
    default:
      return 'Please log in to continue.';
  }
}