import React, { useEffect, useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { useSessionValidation, useNetworkStatus, useAuthRedirect, useTimerCleanup } from './hooks';
import { DEFAULT_TIMEOUTS, SESSION_REFRESH, CUSTOM_EVENTS } from './constants';
import { dispatchCustomEvent, createDelay } from './utils';

interface SessionRefreshManagerProps {
  refreshInterval?: number;
}

const SessionRefreshManager: React.FC<SessionRefreshManagerProps> = ({
  refreshInterval = DEFAULT_TIMEOUTS.REFRESH_INTERVAL,
}) => {
  const { authState } = useAuth();
  const [isRefreshing, setIsRefreshing] = useState(false);
  
  const { validateSession, refreshSession } = useSessionValidation();
  const { isOnline } = useNetworkStatus();
  const { handleSessionExpired } = useAuthRedirect();
  const { setInterval, clearAllTimers } = useTimerCleanup();

  const performSessionRefresh = async () => {
    if (!authState?.isAuthenticated || isRefreshing) {
      return;
    }

    if (!isOnline) {
      console.log('[SessionRefreshManager] Network is offline, skipping session check');
      return;
    }

    setIsRefreshing(true);
    let refreshAttempt = 0;

    try {
      const result = await validateSession();

      if (!result.isValid) {
        console.log('[SessionRefreshManager] Session needs refresh');
        let refreshed = false;

        while (!refreshed && refreshAttempt < SESSION_REFRESH.MAX_ATTEMPTS) {
          refreshAttempt++;
          
          if (!isOnline) {
            console.log('[SessionRefreshManager] Network offline, pausing refresh attempts');
            break;
          }

          refreshed = await refreshSession();

          if (refreshed) {
            dispatchCustomEvent(CUSTOM_EVENTS.SESSION_REFRESHED, {
              timestamp: new Date().toISOString()
            });
            break;
          } else {
            if (refreshAttempt < SESSION_REFRESH.MAX_ATTEMPTS) {
              await createDelay(SESSION_REFRESH.RETRY_DELAY_BASE * refreshAttempt);
            }
          }
        }

        if (!refreshed && isOnline) {
          await handleSessionExpired();
        }
      }
    } catch (error) {
      console.error('[SessionRefreshManager] Error refreshing session:', error);
      if (isOnline) {
        await handleSessionExpired();
      }
    } finally {
      setIsRefreshing(false);
    }
  };


  useEffect(() => {
    if (!authState?.isAuthenticated) {
      return;
    }

    // Initial session check
    const initialCheck = async () => {
      const result = await validateSession();
      if (!result.isValid) {
        await performSessionRefresh();
      }
    };

    initialCheck();

    // Set up interval for regular session refreshes
    const refreshTimer = window.setInterval(() => {
      performSessionRefresh();
    }, refreshInterval);
    
    setInterval('sessionRefresh', refreshTimer);

    // Set up auth state change listener
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event) => {
        if (event === 'SIGNED_OUT') {
          clearAllTimers();
        }
      }
    );

    return () => {
      clearAllTimers();
      subscription.unsubscribe();
    };
  }, [authState?.isAuthenticated, refreshInterval]);

  // Handle network status changes
  useEffect(() => {
    const handleNetworkStatus = (event: CustomEvent) => {
      const { online } = event.detail;
      if (online && authState?.isAuthenticated) {
        setTimeout(() => {
          performSessionRefresh();
        }, 2000);
      }
    };

    window.addEventListener(CUSTOM_EVENTS.NETWORK_STATUS, handleNetworkStatus as EventListener);

    return () => {
      window.removeEventListener(CUSTOM_EVENTS.NETWORK_STATUS, handleNetworkStatus as EventListener);
    };
  }, [authState?.isAuthenticated]);


  // This component doesn't render anything
  return null;
};

export default SessionRefreshManager;
