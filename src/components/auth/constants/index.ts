// Default timeout configurations
export const DEFAULT_TIMEOUTS = {
  IDLE_TIMEOUT: 72 * 60 * 60 * 1000, // 72 hours
  WARNING_TIME: 30 * 60 * 1000, // 30 minutes
  CHECK_INTERVAL: 60 * 60 * 1000, // 1 hour
  REFRESH_INTERVAL: 24 * 60 * 60 * 1000, // 24 hours
  SESSION_CHECK_INTERVAL: 10 * 60 * 1000, // 10 minutes
} as const;

// Storage keys
export const STORAGE_KEYS = {
  AUTH_TOKEN: 'stayfu_auth_token',
  SUPABASE_TOKEN: 'supabase.auth.token',
  HAS_SESSION: 'stayfu_has_session',
  SESSION_EXPIRES: 'stayfu_session_expires',
  REDIRECT_AFTER_LOGIN: 'redirectAfterLogin',
  SESSION_EXPIRED: 'sessionExpired',
  PAGE_REFRESHED: 'stayfu_page_refreshed',
  LAST_TOKEN_REFRESH: 'stayfu_last_token_refresh',
} as const;

// User activity events to monitor
export const ACTIVITY_EVENTS = [
  'mousedown',
  'mousemove',
  'keydown',
  'scroll',
  'touchstart',
  'click',
  'focus',
] as const;

// Custom events
export const CUSTOM_EVENTS = {
  SESSION_REFRESHED: 'stayfu-session-refreshed',
  SESSION_EXPIRED: 'stayfu-session-expired',
  NETWORK_STATUS: 'stayfu-network-status',
} as const;

// Session refresh configuration
export const SESSION_REFRESH = {
  MAX_ATTEMPTS: 3,
  RETRY_DELAY_BASE: 1000, // Base delay in ms, multiplied by attempt number
  GRACE_PERIOD: 24 * 60 * 60, // 24 hours in seconds
} as const;

// Route paths
export const ROUTES = {
  LOGIN: '/#/login',
  DASHBOARD: '/#/dashboard',
} as const;