export interface SessionValidationResult {
  isValid: boolean;
  needsRefresh: boolean;
  error?: string;
}

export interface SessionStorageData {
  session: any;
  user: any;
  expiresAt?: number;
  timestamp: number;
}

export interface TimerRefs {
  idleTimer: React.MutableRefObject<NodeJS.Timeout | null>;
  warningTimer: React.MutableRefObject<NodeJS.Timeout | null>;
  countdownInterval: React.MutableRefObject<NodeJS.Timeout | null>;
  sessionCheckInterval: React.MutableRefObject<NodeJS.Timeout | null>;
}

export interface AuthRedirectOptions {
  reason?: 'session_expired' | 'idle_timeout' | 'permission_denied' | 'not_authenticated';
  returnUrl?: string;
  showToast?: boolean;
  toastMessage?: string;
}

export interface NetworkStatus {
  isOnline: boolean;
  lastChecked: number;
}

export interface IdleTimeoutConfig {
  idleTimeout: number;
  warningTime: number;
  checkInterval: number;
}

export interface SessionRefreshConfig {
  refreshInterval: number;
  maxRetries: number;
  retryDelay: number;
}