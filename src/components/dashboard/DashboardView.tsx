import React, { memo, useMemo, useEffect } from 'react';
import {
  Building2,
  Wrench,
  Package,
  AlertCircle,
  ArrowRight,
  Calendar,
  Circle,
  ShoppingCart,
  BarChart3,
  InfoIcon,
  PieChart,
  Activity,
  CalendarCheck,
  ListTodo,
  PackageSearch,
  ClipboardList
} from 'lucide-react';
import { Property } from '../properties/PropertyCard';
import { MaintenanceTask } from '../maintenance/types';
import { InventoryItem } from '../inventory/types';
import { cn } from '@/lib/utils';
import { useNavigate } from 'react-router-dom';
import { PurchaseOrder } from '@/types/inventory';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"; // Import Tabs components
import StatCard from "@/components/ui/StatCard";

interface Damage {
  id: string;
  title: string;
  propertyName: string;
  status: 'new' | 'pending' | 'completed';
  reportedAt: string; // Assuming this is a formatted string like "Apr 15" or similar
}

interface DashboardViewProps {
  properties: Property[];
  maintenanceTasks: MaintenanceTask[];
  inventoryItems: InventoryItem[];
  damages: Damage[];
  purchaseOrders?: PurchaseOrder[];
  isLoading?: boolean;
  onViewMore: (section: 'properties' | 'maintenance' | 'inventory' | 'damages' | 'purchaseOrders') => void;
  onViewOrder?: (order: PurchaseOrder) => void;
}

// Helper function to calculate days until a date string (handles date ranges like "Apr 24 - Apr 27, 2025")
function calculateDaysUntil(dateStr: string | null | undefined): number {
  if (!dateStr) return Infinity;
  try {
    console.log(`[calculateDaysUntil] Calculating days until: ${dateStr}`);

    // Special case for YYYY-MM-DD format (next_checkin_date)
    if (dateStr.match(/^\d{4}-\d{2}-\d{2}$/)) {
      console.log(`[calculateDaysUntil] Detected ISO date format: ${dateStr}`);

      // Create date objects using the date parts to avoid timezone issues
      const [year, month, day] = dateStr.split('-').map(Number);
      const targetDate = new Date(year, month - 1, day); // month is 0-indexed in JS Date

      // Create today's date at midnight in local timezone
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const todayYear = today.getFullYear();
      const todayMonth = today.getMonth();
      const todayDay = today.getDate();
      const todayNormalized = new Date(todayYear, todayMonth, todayDay);

      // Check if the date is valid
      if (isNaN(targetDate.getTime())) {
        console.error(`[calculateDaysUntil] Invalid ISO date: ${dateStr}`);
        return Infinity;
      }

      const diffTime = targetDate.getTime() - todayNormalized.getTime();
      console.log(`[calculateDaysUntil] Today: ${todayNormalized.toISOString()}, Target: ${targetDate.toISOString()}, Diff ms: ${diffTime}`);

      if (diffTime < 0) {
        console.log(`[calculateDaysUntil] Date ${dateStr} is in the past`);
        return Infinity; // Past dates are not "upcoming"
      }

      const diffDays = Math.round(diffTime / (1000 * 60 * 60 * 24));
      console.log(`[calculateDaysUntil] Days until ${dateStr}: ${diffDays}`);
      return diffDays;
    }
    // Check if the date string is a range (contains a hyphen)
    else if (dateStr.includes('-')) {
      console.log(`[calculateDaysUntil] Detected date range: ${dateStr}`);

      // Extract the start date from the range (e.g., "Apr 24" from "Apr 24 - Apr 27, 2025")
      const parts = dateStr.split('-');
      let startDateStr = parts[0].trim();

      // If the year is only at the end of the range, add it to the start date
      if (!startDateStr.includes(',') && parts[1].includes(',')) {
        const year = parts[1].trim().split(',')[1].trim();
        startDateStr = `${startDateStr}, ${year}`;
      }

      console.log(`[calculateDaysUntil] Extracted start date: ${startDateStr}`);

      // Parse the start date
      const startDate = new Date(startDateStr);

      // Check if the date is valid
      if (isNaN(startDate.getTime())) {
        console.error(`[calculateDaysUntil] Invalid start date: ${startDateStr}`);

        // Try using next_checkin_date if available
        if (typeof window !== 'undefined') {
          const properties = JSON.parse(localStorage.getItem('properties') || '[]');
          const property = properties.find((p: any) => p.next_booking === dateStr);
          if (property && property.next_checkin_date) {
            console.log(`[calculateDaysUntil] Using next_checkin_date instead: ${property.next_checkin_date}`);
            return calculateDaysUntil(property.next_checkin_date);
          }
        }

        return Infinity;
      }

      // Create today's date at midnight in local timezone
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const todayYear = today.getFullYear();
      const todayMonth = today.getMonth();
      const todayDay = today.getDate();
      const todayNormalized = new Date(todayYear, todayMonth, todayDay);

      // Normalize the start date to midnight in local timezone
      const startYear = startDate.getFullYear();
      const startMonth = startDate.getMonth();
      const startDay = startDate.getDate();
      const startDateNormalized = new Date(startYear, startMonth, startDay);

      const diffTime = startDateNormalized.getTime() - todayNormalized.getTime();
      console.log(`[calculateDaysUntil] Today: ${todayNormalized.toISOString()}, Target: ${startDateNormalized.toISOString()}, Diff ms: ${diffTime}`);

      if (diffTime < 0) {
        console.log(`[calculateDaysUntil] Date ${startDateStr} is in the past`);
        return Infinity; // Past dates are not "upcoming"
      }

      const diffDays = Math.round(diffTime / (1000 * 60 * 60 * 24));
      console.log(`[calculateDaysUntil] Days until ${startDateStr}: ${diffDays}`);
      return diffDays;
    } else {
      // Handle single date (not a range)
      // If the date string doesn't include time, add T00:00:00 to ensure consistent parsing
      const normalizedDateStr = dateStr.includes('T') ? dateStr : `${dateStr}T00:00:00`;
      const targetDate = new Date(normalizedDateStr);

      // Check if the date is valid
      if (isNaN(targetDate.getTime())) {
        console.error(`[calculateDaysUntil] Invalid date: ${dateStr}`);
        return Infinity;
      }

      // Create today's date at midnight in local timezone
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const todayYear = today.getFullYear();
      const todayMonth = today.getMonth();
      const todayDay = today.getDate();
      const todayNormalized = new Date(todayYear, todayMonth, todayDay);

      // Normalize the target date to midnight in local timezone
      const targetYear = targetDate.getFullYear();
      const targetMonth = targetDate.getMonth();
      const targetDay = targetDate.getDate();
      const targetDateNormalized = new Date(targetYear, targetMonth, targetDay);

      const diffTime = targetDateNormalized.getTime() - todayNormalized.getTime();
      console.log(`[calculateDaysUntil] Today: ${todayNormalized.toISOString()}, Target: ${targetDateNormalized.toISOString()}, Diff ms: ${diffTime}`);

      if (diffTime < 0) {
        console.log(`[calculateDaysUntil] Date ${dateStr} is in the past`);
        return Infinity; // Past dates are not "upcoming"
      }

      const diffDays = Math.round(diffTime / (1000 * 60 * 60 * 24));
      console.log(`[calculateDaysUntil] Days until ${dateStr}: ${diffDays}`);
      return diffDays;
    }
  } catch (e) {
    console.error("Error parsing date:", dateStr, e);
    return Infinity;
  }
}

// Helper function to format date (handles date ranges)
const formatDate = (dateStr: string | null | undefined): string => {
  if (!dateStr) return 'N/A';
  try {
    console.log(`[formatDate] Formatting date: ${dateStr}`);

    // If it's a date range (contains a hyphen), return it as is
    if (dateStr.includes('-')) {
      console.log(`[formatDate] Date range detected, returning as is: ${dateStr}`);
      return dateStr;
    }

    // For single dates
    // If the date string doesn't include time, add T00:00:00 to ensure consistent parsing
    const normalizedDateStr = dateStr.includes('T') ? dateStr : `${dateStr}T00:00:00`;
    const date = new Date(normalizedDateStr);

    if (isNaN(date.getTime())) {
      console.error(`[formatDate] Invalid date: ${dateStr}`);
      // Return the original string if we can't parse it
      return dateStr;
    }

    const formatted = date.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' });
    console.log(`[formatDate] Formatted ${dateStr} as: ${formatted}`);
    return formatted;
  } catch (e) {
    console.error("Error formatting date:", dateStr, e);
    // Return the original string if we encounter an error
    return dateStr;
  }
};

 // Memoize the component to prevent unnecessary re-renders
 const DashboardView: React.FC<DashboardViewProps> = ({
  properties = [], // Default to empty array
  maintenanceTasks = [], // Default to empty array
  inventoryItems = [], // Default to empty array
  damages = [], // Default to empty array
  purchaseOrders = [],
  isLoading = false,
  onViewMore,
  onViewOrder,
}) => {
  const navigate = useNavigate();

  // Log maintenance tasks when they change
  useEffect(() => {
    console.log('[DashboardView] Maintenance tasks received:', maintenanceTasks);
  }, [maintenanceTasks]);

  // Filter critical tasks
  const criticalTasks = useMemo(() => isLoading ? [] : maintenanceTasks.filter(
    (task) => task && (task.severity === 'critical' || task.severity === 'high')
  ), [maintenanceTasks, isLoading]);

  // Filter low stock items
  const lowStockItems = useMemo(() => isLoading ? [] : inventoryItems.filter(
    (item) => item && typeof item.quantity === 'number' && typeof item.minQuantity === 'number' && item.quantity <= item.minQuantity
  ), [inventoryItems, isLoading]);

  // Filter new damage reports
  const newDamages = useMemo(() => isLoading ? [] : damages.filter((damage) => damage && damage.status === 'new'), [damages, isLoading]);

  // Filter pending purchase orders
  const pendingOrders = useMemo(() => isLoading ? [] : purchaseOrders.filter(
    (order) => order && order.status === 'pending'
  ), [purchaseOrders, isLoading]);

  // Filter upcoming check-ins (within next 7 days, sorted)
  const upcomingCheckins = useMemo(() => {
    if (isLoading) return [];

    console.log('[DashboardView] Processing properties for check-ins:', properties.length);
    console.log('[DashboardView] All properties:', JSON.stringify(properties, null, 2));

    // Log all properties with next_booking or next_checkin_date for debugging
    properties.forEach(p => {
      if (p.next_booking) {
        console.log(`[DashboardView] Property ${p.name} has next_booking: ${p.next_booking}`);
      }
      if (p.next_checkin_date) {
        console.log(`[DashboardView] Property ${p.name} has next_checkin_date: ${p.next_checkin_date}`);
      }
      if (p.next_checkin_formatted) {
        console.log(`[DashboardView] Property ${p.name} has next_checkin_formatted: ${p.next_checkin_formatted}`);
      }
    });

    // Store properties in localStorage for debugging
    if (typeof window !== 'undefined') {
      localStorage.setItem('properties', JSON.stringify(properties));
    }

    // First, create a copy of the properties array to avoid mutation issues
    const propertiesWithDays = properties
      .filter(p => {
        // Include properties that have either next_booking or next_checkin_date
        const hasNextBooking = !!p.next_booking;
        const hasNextCheckinDate = !!p.next_checkin_date;
        console.log(`[DashboardView] Property ${p.name}: has next booking = ${hasNextBooking}, has next checkin date = ${hasNextCheckinDate}`);
        return p && (hasNextBooking || hasNextCheckinDate);
      })
      .map(p => {
        // Try to calculate days using next_checkin_date first if available, then fall back to next_booking
        let daysUntil;
        if (p.next_checkin_date) {
          daysUntil = calculateDaysUntil(p.next_checkin_date);
          console.log(`[DashboardView] Property ${p.name}: using next_checkin_date, days until: ${daysUntil}`);
        } else if (p.next_booking) {
          daysUntil = calculateDaysUntil(p.next_booking);
          console.log(`[DashboardView] Property ${p.name}: using next_booking, days until: ${daysUntil}`);
        } else {
          daysUntil = Infinity;
          console.log(`[DashboardView] Property ${p.name}: no date available, setting days until to Infinity`);
        }

        return { ...p, daysUntil };
      });

    console.log('[DashboardView] Properties with days calculated:', propertiesWithDays.map(p => ({ name: p.name, daysUntil: p.daysUntil })));

    const upcomingProperties = propertiesWithDays
      .filter(p => {
        const isUpcoming = p.daysUntil <= 7 && p.daysUntil !== Infinity;
        console.log(`[DashboardView] Property ${p.name}: is upcoming (≤7 days) = ${isUpcoming}, days until: ${p.daysUntil}`);
        return isUpcoming; // Show check-ins within the next 7 days
      })
      .sort((a, b) => a.daysUntil - b.daysUntil);

    console.log('[DashboardView] Final upcoming properties:', upcomingProperties.map(p => p.name));
    return upcomingProperties;
  }, [properties, isLoading]);


  // Calculate counts for stat cards
  const propertiesCount = isLoading ? '-' : properties.length;
  const criticalTasksCount = isLoading ? '-' : criticalTasks.length;
  const lowStockItemsCount = isLoading ? '-' : lowStockItems.length;
  const pendingOrdersCount = isLoading ? '-' : pendingOrders.length;

   // Calculate specific counts for Property Overview
   const occupiedCount = useMemo(() => isLoading ? '-' : properties.filter(p => p && p.is_occupied).length, [properties, isLoading]);
   const vacantCount = useMemo(() => isLoading ? '-' : properties.filter(p => p && !p.is_occupied).length, [properties, isLoading]);
   const nextCheckin = useMemo(() => isLoading || upcomingCheckins.length === 0 ? null : upcomingCheckins[0], [upcomingCheckins, isLoading]);


    // Event handlers
    const handlePropertyClick = (id: string) => navigate(`/properties/${id}`);
  const handleTaskClick = (id: string) => navigate(`/maintenance?id=${id}`);
  const handleInventoryClick = (id: string) => navigate(`/inventory?itemId=${id}`);
  const handleDamageClick = (id: string) => navigate(`/damages/${id}`);
  const handleOrderClick = (order: PurchaseOrder) => {
    if (onViewOrder) {
      onViewOrder(order);
    } else {
      navigate(`/purchase-orders/${order.id}`);
    }
  };

  // Stat cards configuration with enhanced glassmorphism design
  const statCards = useMemo(() => [
    {
      title: 'Properties',
      value: propertiesCount,
      icon: Building2,
      colorScheme: 'blue' as const,
      onClick: () => onViewMore('properties'),
      alert: false,
      subtitle: `${occupiedCount} occupied, ${vacantCount} vacant`
    },
    {
      title: 'Critical Tasks',
      value: criticalTasksCount,
      icon: Wrench,
      colorScheme: 'amber' as const,
      onClick: () => onViewMore('maintenance'),
      alert: typeof criticalTasksCount === 'number' && criticalTasksCount > 0,
      subtitle: 'Requires immediate attention'
    },
    {
      title: 'Low Stock Items',
      value: lowStockItemsCount,
      icon: Package,
      colorScheme: 'purple' as const,
      onClick: () => onViewMore('inventory'),
      alert: typeof lowStockItemsCount === 'number' && lowStockItemsCount > 0,
      subtitle: 'Need restocking'
    },
    {
      title: 'Pending Orders',
      value: pendingOrdersCount,
      icon: ShoppingCart,
      colorScheme: 'green' as const,
      onClick: () => onViewMore('purchaseOrders'),
      alert: false,
      subtitle: 'Awaiting processing'
    },
  ], [propertiesCount, criticalTasksCount, lowStockItemsCount, pendingOrdersCount, occupiedCount, vacantCount, onViewMore]);


  // Render Loading Skeleton for Cards
  const CardSkeleton = () => (
    <div className="animate-pulse h-48 bg-muted/50 rounded-md" />
  );

  // Render Empty State for Lists
  const EmptyListState = ({ icon: Icon, message }: { icon: React.ElementType, message: string }) => (
    <div className="text-center py-6 text-muted-foreground">
      <Icon className="w-6 h-6 mx-auto mb-2 opacity-40" />
      <p className="text-sm">{message}</p>
    </div>
  );

  return (
    <div className="space-y-4">
      {/* Top Stat Cards - Enhanced Glassmorphism Design */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
        {statCards.map((stat, index) => (
          <StatCard
            key={index}
            title={stat.title}
            value={stat.value}
            icon={stat.icon}
            colorScheme={stat.colorScheme}
            alert={stat.alert}
            loading={isLoading}
            subtitle={stat.subtitle}
            onClick={stat.onClick}
            className="animate-fade-in"
            style={{ animationDelay: `${index * 100}ms` }}
          />
        ))}
      </div>

      {/* Main Content Grid (2 Columns on Large Screens) */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">

        {/* Left Column (Overview Cards) */}
        <div className="lg:col-span-2 space-y-6">
          {/* Properties Overview Card - Enhanced Glassmorphism */}
          <Card className="glass-card border-0 shadow-xl">
            <CardHeader className="py-3 px-4">
              <div className="flex items-center justify-between">
                <CardTitle className="text-base flex items-center gap-2">
                  <Building2 className="h-4 w-4 text-blue-600" />
                  Property Overview
                </CardTitle>
                <button
                  onClick={() => onViewMore('properties')}
                  className="text-sm text-primary hover:underline flex items-center"
                >
                  View All <ArrowRight size={14} className="ml-1" />
                </button>
              </div>
            </CardHeader>
            <CardContent className="p-4 space-y-3">
              {isLoading ? (
                <CardSkeleton />
              ) : properties.length > 0 ? (
                <>
                  {/* Occupied/Vacant Row */}
                  <div className="grid grid-cols-2 gap-4">
                    {/* Occupied */}
                    <div className="flex flex-col items-center justify-center p-4 rounded-lg bg-green-50 dark:bg-green-950/50 border border-green-100 dark:border-green-900">
                      <span className="text-3xl font-bold text-green-700 dark:text-green-400">
                        {occupiedCount}
                      </span>
                      <span className="text-sm text-green-700 dark:text-green-400 mt-1">Occupied</span>
                    </div>
                    {/* Vacant */}
                    <div className="flex flex-col items-center justify-center p-4 rounded-lg bg-red-50 dark:bg-red-950/50 border border-red-100 dark:border-red-900">
                      <span className="text-3xl font-bold text-red-700 dark:text-red-400">
                        {vacantCount}
                      </span>
                      <span className="text-sm text-red-700 dark:text-red-400 mt-1">Vacant</span>
                    </div>
                  </div>

                  {/* Next Check-in Section */}
                  <div className="border-t pt-4 mt-4">
                    <h4 className="text-sm font-medium text-muted-foreground mb-2 flex items-center gap-1.5">
                      <CalendarCheck className="h-4 w-4 text-teal-600" /> Upcoming Check-ins (7 days)
                    </h4>
                    {upcomingCheckins.length > 0 ? (
                      <div className="space-y-2 max-h-32 overflow-y-auto">
                        {upcomingCheckins.map(property => (
                          <div
                            key={property.id}
                            className="flex justify-between items-center text-sm hover:bg-muted/50 p-2 -mx-2 rounded-md cursor-pointer transition-colors"
                            onClick={() => handlePropertyClick(property.id)}
                            title={`View ${property.name}`}
                          >
                            <span className="font-medium truncate">{property.name}</span>
                            <div className="text-right">
                              <span className="text-muted-foreground whitespace-nowrap">
                                {property.next_checkin_formatted || formatDate(property.next_booking || property.next_checkin_date) || 'N/A'}
                              </span>
                              <div className="text-xs text-teal-600">
                                {property.daysUntil === 0 ? 'Today' :
                                 property.daysUntil === 1 ? 'Tomorrow' :
                                 `In ${property.daysUntil} days`}
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <p className="text-sm text-muted-foreground px-2">No upcoming check-ins.</p>
                    )}
                  </div>
                </>
              ) : (
                <EmptyListState icon={Building2} message="No properties added yet" />
              )}
            </CardContent>
          </Card>

          {/* Maintenance Overview Card - Enhanced Glassmorphism */}
          <Card className="glass-card border-0 shadow-xl">
            <CardHeader className="py-3 px-4">
              <div className="flex items-center justify-between">
                <CardTitle className="text-base flex items-center gap-2">
                  <Wrench className="h-4 w-4 text-amber-600" />
                  Maintenance Overview
                </CardTitle>
                <div className="flex items-center gap-2">
                  {/* Manual refresh button for maintenance tasks */}
                  <button
                    onClick={() => {
                      console.log('[DashboardView] Manual maintenance tasks refresh clicked');
                      // Use window.dispatchEvent to trigger a custom event
                      window.dispatchEvent(new CustomEvent('force-refresh-maintenance'));
                    }}
                    className="text-xs px-2 py-1 bg-amber-50 dark:bg-amber-950/50 text-amber-700 dark:text-amber-400 hover:bg-amber-100 dark:hover:bg-amber-900/50 hover:text-amber-800 dark:hover:text-amber-300 border border-amber-200 dark:border-amber-900 rounded-md"
                  >
                    Refresh Tasks
                  </button>
                  <button
                    onClick={() => onViewMore('maintenance')}
                    className="text-sm text-primary hover:underline flex items-center"
                  >
                    View All <ArrowRight size={14} className="ml-1" />
                  </button>
                </div>
              </div>
            </CardHeader>
            <CardContent className="p-4">
              {/* Log maintenance tasks for debugging */}
              {isLoading ? (
                <CardSkeleton />
              ) : maintenanceTasks && maintenanceTasks.length > 0 ? (
                <div className="space-y-4">
                  <div className="grid grid-cols-3 gap-4">
                    {/* Critical Tasks */}
                    <div
                      className="flex flex-col items-center justify-center p-3 rounded-lg bg-red-50 dark:bg-red-950/50 border border-red-100 dark:border-red-900 cursor-pointer hover:bg-red-100 dark:hover:bg-red-900/50 transition-colors"
                      onClick={() => {
                        // Clear any existing session storage filters first
                        window.sessionStorage.removeItem('maintenance_status_filter');
                        window.sessionStorage.removeItem('maintenance_provider_filter');
                        window.sessionStorage.removeItem('maintenance_property_filter');
                        // Set the severity filter directly in session storage
                        window.sessionStorage.setItem('maintenance_severity_filter', 'critical');
                        // Navigate to maintenance page
                        navigate('/maintenance');
                      }}
                    >
                      <span className="text-2xl font-bold text-red-700 dark:text-red-400">
                        {maintenanceTasks.filter(task => task.severity === 'critical').length}
                      </span>
                      <span className="text-xs text-red-700 dark:text-red-400 mt-1">Critical</span>
                    </div>
                    {/* High Priority Tasks */}
                    <div
                      className="flex flex-col items-center justify-center p-3 rounded-lg bg-amber-50 dark:bg-amber-950/50 border border-amber-100 dark:border-amber-900 cursor-pointer hover:bg-amber-100 dark:hover:bg-amber-900/50 transition-colors"
                      onClick={() => {
                        // Clear any existing session storage filters first
                        window.sessionStorage.removeItem('maintenance_status_filter');
                        window.sessionStorage.removeItem('maintenance_provider_filter');
                        window.sessionStorage.removeItem('maintenance_property_filter');
                        // Set the severity filter directly in session storage
                        window.sessionStorage.setItem('maintenance_severity_filter', 'high');
                        // Navigate to maintenance page
                        navigate('/maintenance');
                      }}
                    >
                      <span className="text-2xl font-bold text-amber-700 dark:text-amber-400">
                        {maintenanceTasks.filter(task => task.severity === 'high').length}
                      </span>
                      <span className="text-xs text-amber-700 dark:text-amber-400 mt-1">High</span>
                    </div>
                    {/* Regular Tasks */}
                    <div
                      className="flex flex-col items-center justify-center p-3 rounded-lg bg-blue-50 dark:bg-blue-950/50 border border-blue-100 dark:border-blue-900 cursor-pointer hover:bg-blue-100 dark:hover:bg-blue-900/50 transition-colors"
                      onClick={() => {
                        // Clear any existing session storage filters first
                        window.sessionStorage.removeItem('maintenance_status_filter');
                        window.sessionStorage.removeItem('maintenance_provider_filter');
                        window.sessionStorage.removeItem('maintenance_property_filter');
                        // Set the severity filter directly in session storage
                        window.sessionStorage.setItem('maintenance_severity_filter', 'medium');
                        // Navigate to maintenance page
                        navigate('/maintenance');
                      }}
                    >
                      <span className="text-2xl font-bold text-blue-700 dark:text-blue-400">
                        {maintenanceTasks.filter(task => task.severity === 'medium' || task.severity === 'low').length}
                      </span>
                      <span className="text-xs text-blue-700 dark:text-blue-400 mt-1">Normal</span>
                    </div>
                  </div>
                  {/* Status Breakdown */}
                  <div className="grid grid-cols-2 gap-3">
                    <div
                      className="flex items-center justify-between text-sm p-2 bg-blue-50 dark:bg-blue-950/50 rounded-md cursor-pointer hover:bg-blue-100 dark:hover:bg-blue-900/50 transition-colors"
                      onClick={() => {
                        // Clear any existing session storage filters first
                        window.sessionStorage.removeItem('maintenance_severity_filter');
                        window.sessionStorage.removeItem('maintenance_provider_filter');
                        window.sessionStorage.removeItem('maintenance_property_filter');
                        // Set the status filter directly in session storage
                        window.sessionStorage.setItem('maintenance_status_filter', 'new');
                        // Navigate to maintenance page
                        navigate('/maintenance');
                      }}
                    >
                      <span>New</span>
                      <span className="font-semibold">{maintenanceTasks.filter(task => task.status === 'new').length}</span>
                    </div>
                    <div
                      className="flex items-center justify-between text-sm p-2 bg-purple-50 dark:bg-purple-950/50 rounded-md cursor-pointer hover:bg-purple-100 dark:hover:bg-purple-900/50 transition-colors"
                      onClick={() => {
                        // Clear any existing session storage filters first
                        window.sessionStorage.removeItem('maintenance_severity_filter');
                        window.sessionStorage.removeItem('maintenance_provider_filter');
                        window.sessionStorage.removeItem('maintenance_property_filter');
                        // Set the status filter directly in session storage
                        window.sessionStorage.setItem('maintenance_status_filter', 'assigned');
                        // Navigate to maintenance page
                        navigate('/maintenance');
                      }}
                    >
                      <span>Assigned</span>
                      <span className="font-semibold">{maintenanceTasks.filter(task => task.status === 'assigned').length}</span>
                    </div>
                    <div
                      className="flex items-center justify-between text-sm p-2 bg-yellow-50 dark:bg-yellow-950/50 rounded-md cursor-pointer hover:bg-yellow-100 dark:hover:bg-yellow-900/50 transition-colors"
                      onClick={() => {
                        // Clear any existing session storage filters first
                        window.sessionStorage.removeItem('maintenance_severity_filter');
                        window.sessionStorage.removeItem('maintenance_provider_filter');
                        window.sessionStorage.removeItem('maintenance_property_filter');
                        // Set the status filter directly in session storage
                        window.sessionStorage.setItem('maintenance_status_filter', 'in_progress');
                        // Navigate to maintenance page
                        navigate('/maintenance');
                      }}
                    >
                      <span>In Progress</span>
                      <span className="font-semibold">{maintenanceTasks.filter(task => task.status === 'in_progress').length}</span>
                    </div>
                    <div
                      className="flex items-center justify-between text-sm p-2 bg-green-50 dark:bg-green-950/50 rounded-md cursor-pointer hover:bg-green-100 dark:hover:bg-green-900/50 transition-colors"
                      onClick={() => {
                        // Clear any existing session storage filters first
                        window.sessionStorage.removeItem('maintenance_severity_filter');
                        window.sessionStorage.removeItem('maintenance_provider_filter');
                        window.sessionStorage.removeItem('maintenance_property_filter');
                        // Set the status filter directly in session storage
                        window.sessionStorage.setItem('maintenance_status_filter', 'completed');
                        // Set showCompleted to true
                        window.sessionStorage.setItem('maintenance_show_completed', 'true');
                        // Navigate to maintenance page
                        navigate('/maintenance');
                      }}
                    >
                      <span>Completed</span>
                      <span className="font-semibold">{maintenanceTasks.filter(task => task.status === 'completed').length}</span>
                    </div>
                  </div>
                </div>
              ) : (
                <EmptyListState icon={Wrench} message="No maintenance tasks" />
              )}
            </CardContent>
          </Card>
        </div>

        {/* Right Column (Action Items Tab Card) */}
        <div className="lg:col-span-1">
          <Card className="glass-card border-0 shadow-xl">
            <CardHeader className="py-3 px-4">
              <CardTitle className="text-base flex items-center gap-2">
                <Activity className="h-4 w-4" />
                Action Items
              </CardTitle>
            </CardHeader>
            <CardContent className="p-0"> {/* Remove padding for Tabs */}
              <Tabs defaultValue="tasks" className="w-full">
                <TabsList className="grid w-full grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 h-auto p-1 gap-1"> {/* Responsive grid cols */}
                  <TabsTrigger value="tasks" className="flex flex-col h-auto p-1.5 text-xs gap-1 min-w-0">
                    <Wrench className="h-3 w-3 sm:h-4 sm:w-4" />
                    <span className="truncate">Tasks</span>
                    <span className={cn("px-1.5 py-0.5 rounded-full text-xs font-semibold", criticalTasks.length > 0 ? "bg-red-100 text-red-700 dark:bg-red-900/50 dark:text-red-300" : "bg-muted/50 text-muted-foreground")}>{criticalTasks.length}</span>
                  </TabsTrigger>
                  <TabsTrigger value="stock" className="flex flex-col h-auto p-1.5 text-xs gap-1 min-w-0">
                    <Package className="h-3 w-3 sm:h-4 sm:w-4" />
                    <span className="truncate">Stock</span>
                    <span className={cn("px-1.5 py-0.5 rounded-full text-xs font-semibold", lowStockItems.length > 0 ? "bg-purple-100 text-purple-700 dark:bg-purple-900/50 dark:text-purple-300" : "bg-muted/50 text-muted-foreground")}>{lowStockItems.length}</span>
                  </TabsTrigger>
                  <TabsTrigger value="damages" className="flex flex-col h-auto p-1.5 text-xs gap-1 min-w-0">
                    <AlertCircle className="h-3 w-3 sm:h-4 sm:w-4" />
                    <span className="truncate">Damages</span>
                    <span className={cn("px-1.5 py-0.5 rounded-full text-xs font-semibold", newDamages.length > 0 ? "bg-blue-100 text-blue-700 dark:bg-blue-900/50 dark:text-blue-300" : "bg-muted/50 text-muted-foreground")}>{newDamages.length}</span>
                  </TabsTrigger>
                  <TabsTrigger value="orders" className="flex flex-col h-auto p-1.5 text-xs gap-1 min-w-0">
                    <ShoppingCart className="h-3 w-3 sm:h-4 sm:w-4" />
                    <span className="truncate">Orders</span>
                    <span className={cn("px-1.5 py-0.5 rounded-full text-xs font-semibold", pendingOrders.length > 0 ? "bg-green-100 text-green-700 dark:bg-green-900/50 dark:text-green-300" : "bg-muted/50 text-muted-foreground")}>{pendingOrders.length}</span>
                  </TabsTrigger>
                  <TabsTrigger value="checkins" className="flex flex-col h-auto p-1.5 text-xs gap-1 min-w-0">
                    <CalendarCheck className="h-3 w-3 sm:h-4 sm:w-4" />
                    <span className="truncate">Check-ins</span>
                    <span className={cn("px-1.5 py-0.5 rounded-full text-xs font-semibold", upcomingCheckins.length > 0 ? "bg-teal-100 text-teal-700 dark:bg-teal-900/50 dark:text-teal-300" : "bg-muted/50 text-muted-foreground")}>{upcomingCheckins.length}</span>
                  </TabsTrigger>
                </TabsList>

                {/* Critical Tasks Tab */}
                <TabsContent value="tasks" className="p-4 max-h-[400px] overflow-y-auto">
                  {isLoading ? (
                    <CardSkeleton />
                  ) : criticalTasks.length > 0 ? (
                    <div className="space-y-3">
                      {criticalTasks.map((task) => (
                        <CompactTaskItem
                          key={task.id}
                          title={task.title}
                          subtitle={task.propertyName || ''}
                          status={task.status}
                          dueDate={task.dueDate || 'No due date'}
                          onClick={() => handleTaskClick(task.id)}
                          severity={task.severity}
                        />
                      ))}
                    </div>
                  ) : (
                    <EmptyListState icon={Wrench} message="No critical tasks" />
                  )}
                </TabsContent>

                {/* Low Stock Items Tab */}
                <TabsContent value="stock" className="p-4 max-h-[400px] overflow-y-auto">
                  {isLoading ? (
                    <CardSkeleton />
                  ) : lowStockItems.length > 0 ? (
                    <div className="space-y-3">
                      {lowStockItems.map((item) => (
                        <CompactInventoryItem
                          key={item.id}
                          name={item.name}
                          quantity={item.quantity}
                          minQuantity={item.minQuantity}
                          onClick={() => handleInventoryClick(item.id)}
                        />
                      ))}
                    </div>
                  ) : (
                    <EmptyListState icon={Package} message="No items low on stock" />
                  )}
                </TabsContent>

                {/* New Damages Tab */}
                <TabsContent value="damages" className="p-4 max-h-[400px] overflow-y-auto">
                  {isLoading ? (
                    <CardSkeleton />
                  ) : newDamages.length > 0 ? (
                    <div className="space-y-3">
                      {newDamages.map((damage) => (
                        <CompactDamageItem
                          key={damage.id}
                          title={damage.title}
                          propertyName={damage.propertyName}
                          reportedAt={damage.reportedAt}
                          status={damage.status}
                          onClick={() => handleDamageClick(damage.id)}
                        />
                      ))}
                    </div>
                  ) : (
                    <EmptyListState icon={AlertCircle} message="No new damage reports" />
                  )}
                </TabsContent>

                {/* Pending Orders Tab */}
                <TabsContent value="orders" className="p-4 max-h-[400px] overflow-y-auto">
                  {isLoading ? (
                    <CardSkeleton />
                  ) : pendingOrders.length > 0 ? (
                    <div className="space-y-3">
                      {pendingOrders.map((order) => (
                        <CompactOrderItem
                          key={order.id}
                          // Use ID fallback as order_number doesn't exist
                          orderNumber={`Order #${order.id.substring(0, 6)}`}
                          // Use property_name as subtitle instead of non-existent supplier
                          supplier={order.property_name || 'N/A'}
                          status={order.status}
                          itemCount={order.items?.length || 0}
                          onClick={() => handleOrderClick(order)}
                        />
                      ))}
                    </div>
                  ) : (
                    <EmptyListState icon={ShoppingCart} message="No pending purchase orders" />
                  )}
                </TabsContent>

                {/* Upcoming Check-ins Tab */}
                <TabsContent value="checkins" className="p-4 max-h-[400px] overflow-y-auto">
                  {isLoading ? (
                    <CardSkeleton />
                  ) : upcomingCheckins.length > 0 ? (
                    <div className="space-y-3">
                      {upcomingCheckins.map((property) => (
                        <CompactCheckinItem
                          key={property.id}
                          propertyName={property.name}
                          daysUntil={property.daysUntil}
                          onClick={() => handlePropertyClick(property.id)}
                        />
                      ))}
                    </div>
                  ) : (
                    <EmptyListState icon={CalendarCheck} message="No upcoming check-ins (next 7 days)" />
                  )}
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

// --- Compact Item Components (Potentially move to separate files later) ---

// Compact Task Item (Added Severity)
const CompactTaskItem = ({ title, subtitle, status, dueDate, onClick, severity }: {
  title: string;
  subtitle: string;
  status: string;
  dueDate: string;
  onClick: () => void;
  severity?: 'low' | 'medium' | 'high' | 'critical';
}) => {
  const statusStyles = useMemo(() => {
    switch (status) {
      case 'new': return { icon: Circle, color: 'text-blue-500 dark:text-blue-400', bg: 'bg-blue-100 dark:bg-blue-950/50' };
      case 'assigned': return { icon: Circle, color: 'text-purple-500 dark:text-purple-400', bg: 'bg-purple-100 dark:bg-purple-950/50' };
      case 'in_progress': return { icon: Circle, color: 'text-yellow-500 dark:text-yellow-400', bg: 'bg-yellow-100 dark:bg-yellow-950/50' };
      case 'completed': return { icon: Circle, color: 'text-green-500 dark:text-green-400', bg: 'bg-green-100 dark:bg-green-950/50' };
      default: return { icon: Circle, color: 'text-gray-500 dark:text-gray-400', bg: 'bg-gray-100 dark:bg-gray-800' };
    }
  }, [status]);

  const severityStyles = useMemo(() => {
    switch (severity) {
      case 'critical': return "border-l-4 border-red-500";
      case 'high': return "border-l-4 border-amber-500";
      default: return "border-l-4 border-transparent";
    }
  }, [severity]);

  return (
    <div
      className={cn("flex items-center p-3 rounded-md hover:bg-muted/50 cursor-pointer transition-colors", severityStyles)}
      onClick={onClick}
    >
      <span className={cn("mr-3 p-1 rounded-full", statusStyles.bg)}>
        <statusStyles.icon className={cn("h-2.5 w-2.5", statusStyles.color)} fill={statusStyles.color} />
      </span>
      <div className="flex-1 min-w-0">
        <p className="text-sm font-medium truncate">{title}</p>
        <p className="text-xs text-muted-foreground truncate">{subtitle}</p>
      </div>
      <div className="ml-2 text-right">
        <p className="text-xs text-muted-foreground whitespace-nowrap">{dueDate}</p>
        <p className="text-xs capitalize font-medium" style={{ color: statusStyles.color }}>{status.replace('_', ' ')}</p>
      </div>
    </div>
  );
};

// Compact Inventory Item
const CompactInventoryItem = ({ name, quantity, minQuantity, onClick }: {
  name: string;
  quantity: number | null | undefined;
  minQuantity: number | null | undefined;
  onClick: () => void;
}) => {
  const isLow = typeof quantity === 'number' && typeof minQuantity === 'number' && quantity <= minQuantity;
  return (
    <div
      className={cn(
        "flex items-center p-3 rounded-md hover:bg-muted/50 cursor-pointer transition-colors",
        isLow && "border-l-4 border-purple-500"
      )}
      onClick={onClick}
    >
      <span className={cn("mr-3 p-1 rounded-full", isLow ? "bg-purple-100 dark:bg-purple-950/50" : "bg-gray-100 dark:bg-gray-800")}>
        <Package className={cn("h-4 w-4", isLow ? "text-purple-600 dark:text-purple-400" : "text-gray-500 dark:text-gray-400")} />
      </span>
      <div className="flex-1 min-w-0">
        <p className="text-sm font-medium truncate">{name}</p>
      </div>
      <div className="ml-2 text-right">
        <p className={cn("text-sm font-semibold", isLow ? "text-purple-600 dark:text-purple-400" : "text-gray-700 dark:text-gray-300")}>
          {quantity ?? 'N/A'}
        </p>
        <p className="text-xs text-muted-foreground">Min: {minQuantity ?? 'N/A'}</p>
      </div>
    </div>
  );
};

// Compact Damage Item
const CompactDamageItem = ({ title, propertyName, reportedAt, status, onClick }: {
  title: string;
  propertyName: string;
  reportedAt: string;
  status: 'new' | 'pending' | 'completed';
  onClick: () => void;
}) => {
  const statusStyles = useMemo(() => {
    switch (status) {
      case 'new': return { icon: AlertCircle, color: 'text-blue-500 dark:text-blue-400', bg: 'bg-blue-100 dark:bg-blue-950/50' };
      case 'pending': return { icon: AlertCircle, color: 'text-amber-500 dark:text-amber-400', bg: 'bg-amber-100 dark:bg-amber-950/50' };
      case 'completed': return { icon: AlertCircle, color: 'text-green-500 dark:text-green-400', bg: 'bg-green-100 dark:bg-green-950/50' };
      default: return { icon: AlertCircle, color: 'text-gray-500 dark:text-gray-400', bg: 'bg-gray-100 dark:bg-gray-800' };
    }
  }, [status]);

  return (
    <div
      className={cn(
        "flex items-center p-3 rounded-md hover:bg-muted/50 cursor-pointer transition-colors",
        status === 'new' && "border-l-4 border-blue-500" // Highlight new damages
      )}
      onClick={onClick}
    >
      <span className={cn("mr-3 p-1 rounded-full", statusStyles.bg)}>
        <statusStyles.icon className={cn("h-4 w-4", statusStyles.color)} />
      </span>
      <div className="flex-1 min-w-0">
        <p className="text-sm font-medium truncate">{title}</p>
        <p className="text-xs text-muted-foreground truncate">{propertyName}</p>
      </div>
      <div className="ml-2 text-right">
        <p className="text-xs text-muted-foreground whitespace-nowrap">{reportedAt}</p>
         <p className="text-xs capitalize font-medium" style={{ color: statusStyles.color }}>{status}</p>
      </div>
    </div>
  );
};

// Compact Order Item
const CompactOrderItem = ({ orderNumber, supplier, status, itemCount, onClick }: {
  orderNumber: string;
  supplier: string; // Changed from 'supplier' to represent property_name now
  status: string;
  itemCount: number;
  onClick: () => void;
}) => {
   const statusStyles = useMemo(() => {
    switch (status) {
      case 'pending': return { icon: ShoppingCart, color: 'text-green-500 dark:text-green-400', bg: 'bg-green-100 dark:bg-green-950/50' };
      case 'ordered': return { icon: ShoppingCart, color: 'text-blue-500 dark:text-blue-400', bg: 'bg-blue-100 dark:bg-blue-950/50' };
      case 'received': return { icon: ShoppingCart, color: 'text-purple-500 dark:text-purple-400', bg: 'bg-purple-100 dark:bg-purple-950/50' };
      default: return { icon: ShoppingCart, color: 'text-gray-500 dark:text-gray-400', bg: 'bg-gray-100 dark:bg-gray-800' };
    }
  }, [status]);

  return (
    <div
      className={cn(
        "flex items-center p-3 rounded-md hover:bg-muted/50 cursor-pointer transition-colors",
         status === 'pending' && "border-l-4 border-green-500" // Highlight pending orders
        )}
      onClick={onClick}
    >
       <span className={cn("mr-3 p-1 rounded-full", statusStyles.bg)}>
        <statusStyles.icon className={cn("h-4 w-4", statusStyles.color)} />
      </span>
      <div className="flex-1 min-w-0">
        <p className="text-sm font-medium truncate">{orderNumber}</p>
        {/* Display property name as subtitle */}
        <p className="text-xs text-muted-foreground truncate">{supplier}</p>
      </div>
      <div className="ml-2 text-right">
        <p className="text-xs text-muted-foreground">{itemCount} item(s)</p>
         <p className="text-xs capitalize font-medium" style={{ color: statusStyles.color }}>{status}</p>
      </div>
    </div>
  );
};

// Compact Check-in Item
const CompactCheckinItem = ({ propertyName, daysUntil, onClick }: {
  propertyName: string;
  daysUntil: number;
  onClick: () => void;
}) => {
  const urgencyColor = useMemo(() => {
    if (daysUntil === 0) return "text-blue-600"; // Today
    if (daysUntil <= 3) return "text-amber-600"; // Soon
    return "text-green-600"; // Further out
  }, [daysUntil]);

  const urgencyText = useMemo(() => {
    if (daysUntil === 0) return "Today";
    if (daysUntil === 1) return "Tomorrow";
    return `In ${daysUntil} days`;
  }, [daysUntil]);

  return (
    <div
      className={cn(
        "flex items-center p-3 rounded-md hover:bg-muted/50 cursor-pointer transition-colors",
        daysUntil <= 1 && "border-l-4 border-blue-500" // Highlight today/tomorrow
      )}
      onClick={onClick}
    >
      <span className={cn("mr-3 p-1 rounded-full", daysUntil <= 1 ? "bg-blue-100" : "bg-teal-100")}>
        <CalendarCheck className={cn("h-4 w-4", daysUntil <= 1 ? "text-blue-600" : "text-teal-600")} />
      </span>
      <div className="flex-1 min-w-0">
        <p className="text-sm font-medium truncate">{propertyName}</p>
      </div>
      <div className="ml-2 text-right">
        <p className={cn("text-sm font-semibold whitespace-nowrap", urgencyColor)}>
          {urgencyText}
        </p>
      </div>
    </div>
  );
};


export default memo(DashboardView);
