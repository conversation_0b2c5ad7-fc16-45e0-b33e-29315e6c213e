
import { forwardRef } from 'react';
import { cn } from '@/lib/utils';
import Tooltip from './Tooltip';

interface GlassCardProps extends React.HTMLAttributes<HTMLDivElement> {
  hoverEffect?: boolean;
  children: React.ReactNode;
  className?: string;
  interactive?: boolean;
  noPadding?: boolean;
  fullHeight?: boolean;
  tooltip?: string;
  badge?: React.ReactNode;
  animateEntry?: boolean;
  variant?: 'default' | 'stat' | 'content' | 'interactive' | 'floating';
  colorScheme?: 'default' | 'blue' | 'amber' | 'purple' | 'green';
  glowEffect?: boolean;
  floatAnimation?: boolean;
}

const GlassCard = forwardRef<HTMLDivElement, GlassCardProps>(
  ({
    className,
    hoverEffect = false,
    children,
    interactive = false,
    noPadding = false,
    fullHeight = false,
    tooltip,
    badge,
    animateEntry = false,
    variant = 'default',
    colorScheme = 'default',
    glowEffect = false,
    floatAnimation = false,
    ...props
  }, ref) => {

    // Get variant-specific classes
    const getVariantClasses = () => {
      switch (variant) {
        case 'stat':
          return cn(
            'glass-stat rounded-xl sm:rounded-2xl',
            colorScheme === 'blue' && 'glass-stat-blue',
            colorScheme === 'amber' && 'glass-stat-amber',
            colorScheme === 'purple' && 'glass-stat-purple',
            colorScheme === 'green' && 'glass-stat-green',
            'hover:scale-105 transition-all duration-300'
          );
        case 'content':
          return 'glass-card rounded-lg sm:rounded-xl';
        case 'interactive':
          return 'glass-interactive rounded-lg sm:rounded-xl';
        case 'floating':
          return cn(
            'glass-card rounded-xl sm:rounded-2xl shadow-2xl',
            floatAnimation && 'animate-glass-float'
          );
        default:
          return 'glass rounded-lg sm:rounded-xl';
      }
    };

    return (
      <div
        ref={ref}
        className={cn(
          getVariantClasses(),
          'transition-all duration-300 relative',
          !noPadding && 'p-3 sm:p-4 md:p-6',
          hoverEffect && 'glass-card-hover cursor-pointer active:scale-[0.98]',
          interactive && 'hover:border-primary/30 active:border-primary/50',
          fullHeight && 'h-full',
          animateEntry && 'animate-fade-in',
          glowEffect && 'animate-glass-glow',
          floatAnimation && 'animate-glass-float',
          className
        )}
        {...props}
      >
        {tooltip && (
          <Tooltip 
            text={tooltip} 
            position="top"
            className="z-40"
          >
            <div className="absolute top-2 right-2 text-muted-foreground">
              <span className="sr-only">Info</span>
            </div>
          </Tooltip>
        )}
        
        {badge && (
          <div className="absolute top-0 right-0 -mt-2 -mr-2 z-10">
            {badge}
          </div>
        )}
        
        {children}
      </div>
    );
  }
);

GlassCard.displayName = 'GlassCard';

export default GlassCard;
