import { forwardRef } from 'react';
import { cn } from '@/lib/utils';
import { LucideIcon } from 'lucide-react';
import GlassCard from './GlassCard';

interface StatCardProps extends React.HTMLAttributes<HTMLDivElement> {
  title: string;
  value: string | number;
  icon: LucideIcon;
  colorScheme?: 'blue' | 'amber' | 'purple' | 'green';
  trend?: {
    value: number;
    isPositive: boolean;
  };
  alert?: boolean;
  loading?: boolean;
  subtitle?: string;
  className?: string;
}

const StatCard = forwardRef<HTMLDivElement, StatCardProps>(
  ({ 
    title,
    value,
    icon: Icon,
    colorScheme = 'blue',
    trend,
    alert = false,
    loading = false,
    subtitle,
    className,
    onClick,
    ...props 
  }, ref) => {
    
    const getIconColorClass = () => {
      switch (colorScheme) {
        case 'blue':
          return 'text-blue-600 dark:text-blue-400';
        case 'amber':
          return 'text-amber-600 dark:text-amber-400';
        case 'purple':
          return 'text-purple-600 dark:text-purple-400';
        case 'green':
          return 'text-green-600 dark:text-green-400';
        default:
          return 'text-blue-600 dark:text-blue-400';
      }
    };

    const getIconBgClass = () => {
      switch (colorScheme) {
        case 'blue':
          return 'bg-blue-100 dark:bg-blue-900/30';
        case 'amber':
          return 'bg-amber-100 dark:bg-amber-900/30';
        case 'purple':
          return 'bg-purple-100 dark:bg-purple-900/30';
        case 'green':
          return 'bg-green-100 dark:bg-green-900/30';
        default:
          return 'bg-blue-100 dark:bg-blue-900/30';
      }
    };

    return (
      <GlassCard
        ref={ref}
        variant="stat"
        colorScheme={colorScheme}
        hoverEffect={!!onClick}
        glowEffect={alert}
        noPadding
        className={cn(
          "overflow-hidden group",
          alert && "ring-2 ring-red-500/50 ring-offset-2 ring-offset-background",
          onClick && "cursor-pointer",
          className
        )}
        onClick={onClick}
        {...props}
      >
        <div className="p-3 sm:p-4">
          <div className="flex items-start justify-between">
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-2">
                <div className={cn(
                  "p-2 rounded-lg transition-all duration-300 group-hover:scale-110",
                  getIconBgClass()
                )}>
                  <Icon className={cn("h-4 w-4 sm:h-5 sm:w-5", getIconColorClass())} />
                </div>
                {alert && (
                  <div className="flex-shrink-0">
                    <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse" />
                  </div>
                )}
              </div>
              
              <div className="space-y-0.5">
                <p className="text-lg sm:text-xl lg:text-2xl font-bold text-foreground transition-all duration-300 group-hover:scale-105">
                  {loading ? (
                    <span className="inline-block w-12 h-6 bg-muted animate-pulse rounded" />
                  ) : (
                    value
                  )}
                </p>
                <p className="text-xs font-medium text-muted-foreground">
                  {title}
                </p>
                {subtitle && (
                  <p className="text-xs text-muted-foreground/70 leading-tight">
                    {subtitle}
                  </p>
                )}
              </div>
            </div>
            
            {trend && (
              <div className={cn(
                "flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium",
                trend.isPositive 
                  ? "bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400"
                  : "bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400"
              )}>
                <span className={cn(
                  "text-xs",
                  trend.isPositive ? "text-green-600" : "text-red-600"
                )}>
                  {trend.isPositive ? "↗" : "↘"}
                </span>
                {Math.abs(trend.value)}%
              </div>
            )}
          </div>
        </div>
        
        {/* Subtle gradient overlay for extra depth */}
        <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent dark:from-white/2 pointer-events-none" />
      </GlassCard>
    );
  }
);

StatCard.displayName = 'StatCard';

export default StatCard;
