
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 200 100% 40%;
    --primary-foreground: 0 0% 100%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 200 100% 40%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 200 100% 40%;

    --radius: 0.5rem;

    --sidebar-background: 200 100% 40%;
    --sidebar-foreground: 0 0% 100%;
    --sidebar-primary: 0 0% 100%;
    --sidebar-primary-foreground: 200 100% 40%;
    --sidebar-accent: 200 90% 45%;
    --sidebar-accent-foreground: 0 0% 100%;
    --sidebar-border: 200 80% 35%;
    --sidebar-ring: 0 0% 100%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 200 100% 40%;
    --primary-foreground: 0 0% 100%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 200 100% 40%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 200 100% 40%;

    --sidebar-background: 222.2 84% 4.9%;
    --sidebar-foreground: 210 40% 98%;
    --sidebar-primary: 200 100% 40%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 217.2 32.6% 17.5%;
    --sidebar-accent-foreground: 210 40% 98%;
    --sidebar-border: 217.2 32.6% 17.5%;
    --sidebar-ring: 200 100% 40%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  html {
    @apply scroll-smooth antialiased;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "ss01", "ss02", "cv01", "cv02", "cv03";
    font-optical-sizing: auto;
    background: linear-gradient(135deg, hsl(var(--background)) 0%, hsl(var(--background)) 100%);
    letter-spacing: -0.01em; /* Slightly tighter letter spacing for modern look */
  }

  /* Enhanced Typography for Glassmorphism */
  h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    letter-spacing: -0.025em;
    line-height: 1.2;
  }

  .text-display {
    font-weight: 700;
    letter-spacing: -0.04em;
    line-height: 1.1;
  }

  .text-body {
    line-height: 1.6;
    letter-spacing: -0.01em;
  }

  .text-caption {
    font-size: 0.875rem;
    line-height: 1.4;
    letter-spacing: 0.01em;
  }

  /* Modern Dashboard Background */
  .dashboard-background {
    background: linear-gradient(135deg,
      hsl(var(--background)) 0%,
      hsl(var(--muted)/0.3) 25%,
      hsl(var(--background)) 50%,
      hsl(var(--accent)/0.1) 75%,
      hsl(var(--background)) 100%);
    min-height: 100vh;
  }

  .dark .dashboard-background {
    background: linear-gradient(135deg,
      hsl(var(--background)) 0%,
      hsl(var(--muted)/0.2) 25%,
      hsl(var(--background)) 50%,
      hsl(var(--accent)/0.05) 75%,
      hsl(var(--background)) 100%);
  }
}

@layer utilities {
  /* Enhanced Glass Morphism System */
  .glass {
    @apply bg-white/20 dark:bg-black/20 backdrop-blur-xl border border-white/30 dark:border-white/10 shadow-lg;
  }

  .glass-hover {
    @apply hover:bg-white/30 dark:hover:bg-black/30 hover:border-white/40 dark:hover:border-white/20 transition-all duration-300 hover:shadow-xl;
  }

  /* Glass Card Variants */
  .glass-card {
    @apply bg-gradient-to-br from-white/25 to-white/10 dark:from-black/25 dark:to-black/10 backdrop-blur-xl border border-white/30 dark:border-white/10 shadow-2xl;
  }

  .glass-card-hover {
    @apply hover:from-white/35 hover:to-white/15 dark:hover:from-black/35 dark:hover:to-black/15 hover:border-white/40 dark:hover:border-white/20 transition-all duration-500 hover:shadow-2xl hover:scale-[1.02];
  }

  /* Stat Card Glass Effect */
  .glass-stat {
    @apply bg-gradient-to-br backdrop-blur-2xl border border-white/40 dark:border-white/15 shadow-xl;
  }

  .glass-stat-blue {
    @apply from-blue-500/30 to-blue-600/15 dark:from-blue-400/25 dark:to-blue-500/15 hover:from-blue-500/40 hover:to-blue-600/25;
  }

  .glass-stat-amber {
    @apply from-amber-500/30 to-amber-600/15 dark:from-amber-400/25 dark:to-amber-500/15 hover:from-amber-500/40 hover:to-amber-600/25;
  }

  .glass-stat-purple {
    @apply from-purple-500/30 to-purple-600/15 dark:from-purple-400/25 dark:to-purple-500/15 hover:from-purple-500/40 hover:to-purple-600/25;
  }

  .glass-stat-green {
    @apply from-green-500/30 to-green-600/15 dark:from-green-400/25 dark:to-green-500/15 hover:from-green-500/40 hover:to-green-600/25;
  }

  /* Interactive Glass Elements */
  .glass-interactive {
    @apply glass-card glass-card-hover cursor-pointer active:scale-[0.98] active:shadow-lg;
  }

  /* Glass Background Overlays */
  .glass-overlay {
    @apply bg-gradient-to-br from-white/10 to-transparent dark:from-black/10 dark:to-transparent backdrop-blur-sm;
  }

  /* Enhanced Glass Borders for Light Mode */
  .glass-border {
    @apply border border-white/40 dark:border-white/10;
  }

  .glass-border-strong {
    @apply border border-white/60 dark:border-white/20;
  }

  .text-balance {
    text-wrap: balance;
  }

  .text-gradient {
    @apply bg-clip-text text-transparent bg-gradient-to-r from-primary to-primary/70;
  }

  .text-gradient-blue {
    @apply bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-blue-400;
  }

  .text-gradient-purple {
    @apply bg-clip-text text-transparent bg-gradient-to-r from-purple-600 to-purple-400;
  }

  /* Mobile-First Glassmorphism Enhancements */
  @media (max-width: 640px) {
    .glass-card {
      @apply backdrop-blur-lg; /* Reduce blur on mobile for better performance */
    }

    .glass-stat {
      @apply backdrop-blur-lg; /* Reduce blur on mobile for better performance */
    }

    /* Improve touch targets on mobile */
    .glass-interactive {
      @apply min-h-[44px] min-w-[44px]; /* Ensure minimum touch target size */
    }

    /* Reduce animations on mobile for better performance */
    .animate-glass-float {
      animation: none;
    }

    /* Optimize glass effects for mobile */
    .glass-card-hover:active {
      @apply scale-[0.95] shadow-lg; /* Provide immediate feedback on touch */
    }
  }

  /* Enhanced touch feedback for mobile */
  @media (hover: none) and (pointer: coarse) {
    .glass-card-hover:hover {
      @apply scale-100 shadow-xl; /* Reset hover effects on touch devices */
    }

    .glass-card-hover:active {
      @apply scale-[0.98] shadow-lg; /* Active state for touch */
    }
  }

  /* Advanced Animation Utilities */
  .animate-stagger-in {
    animation: staggerIn 0.6s ease-out forwards;
  }

  .animate-bounce-subtle {
    animation: bounceSubtle 2s ease-in-out infinite;
  }

  .animate-pulse-glow {
    animation: pulseGlow 3s ease-in-out infinite;
  }

  .animate-slide-up-fade {
    animation: slideUpFade 0.5s ease-out forwards;
  }

  /* Loading shimmer effect */
  .animate-shimmer {
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
  }

  /* Micro-interaction animations */
  .hover-lift {
    transition: transform 0.2s ease-out, box-shadow 0.2s ease-out;
  }

  .hover-lift:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }

  /* Enhanced Color Utilities for Glassmorphism */
  .text-glass {
    @apply text-foreground/90;
  }

  .text-glass-muted {
    @apply text-muted-foreground/80;
  }

  .bg-glass-white {
    background: rgba(255, 255, 255, 0.1);
  }

  .bg-glass-black {
    background: rgba(0, 0, 0, 0.1);
  }

  .border-glass {
    border-color: rgba(255, 255, 255, 0.2);
  }

  .dark .border-glass {
    border-color: rgba(255, 255, 255, 0.1);
  }

  /* Accessibility improvements */
  @media (prefers-reduced-motion: reduce) {
    .animate-glass-float,
    .animate-glass-glow,
    .animate-bounce-subtle,
    .animate-pulse-glow {
      animation: none;
    }

    .glass-card-hover {
      transition: none;
    }
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    .glass,
    .glass-card,
    .glass-stat {
      @apply bg-background border-2 border-foreground;
      backdrop-filter: none;
    }
  }

  .sidebar {
    @apply bg-[hsl(var(--sidebar-background))] text-[hsl(var(--sidebar-foreground))] border-r border-[hsl(var(--sidebar-border))] shadow-lg;
  }

  .sidebar-item {
    @apply flex items-center gap-3 px-4 py-3 rounded-md transition-colors;
  }

  .sidebar-item-active {
    @apply bg-[hsl(var(--sidebar-accent))] text-[hsl(var(--sidebar-accent-foreground))];
  }

  .sidebar-item-inactive {
    @apply hover:bg-[hsl(var(--sidebar-accent))/20] text-[hsl(var(--sidebar-foreground))];
  }

  .stat-card {
    @apply bg-card rounded-md p-4 shadow-sm border border-border;
  }

  .stat-value {
    @apply text-2xl font-semibold text-card-foreground;
  }

  .stat-label {
    @apply text-sm text-muted-foreground;
  }

  .chart-container {
    @apply bg-card rounded-md p-4 shadow-sm border border-border;
  }

  .filter-dropdown {
    @apply bg-card border border-border rounded-md px-3 py-1.5 text-sm text-card-foreground;
  }
}

/* Compact mode */
.compact-mode .card {
  padding: 0.75rem;
}

.compact-mode .p-4 {
  padding: 0.5rem;
}

.compact-mode .p-6 {
  padding: 0.75rem;
}

.compact-mode .py-4 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.compact-mode .py-6 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

.compact-mode .gap-6 {
  gap: 0.75rem;
}

.compact-mode .gap-4 {
  gap: 0.5rem;
}

.compact-mode .space-y-6 > * + * {
  margin-top: 0.75rem;
}

.compact-mode .space-y-4 > * + * {
  margin-top: 0.5rem;
}

.compact-mode .text-lg {
  font-size: 1rem; /* text-base size */
  line-height: 1.5rem; /* text-base line height */
}

.compact-mode .text-xl {
  font-size: 1.125rem; /* text-lg size */
  line-height: 1.75rem; /* text-lg line height */
}

.compact-mode .text-2xl {
  font-size: 1.25rem; /* text-xl size */
  line-height: 1.75rem; /* text-xl line height */
}

.compact-mode .mb-4 {
  margin-bottom: 0.5rem;
}

.compact-mode .mb-6 {
  margin-bottom: 0.75rem;
}

.compact-mode .mt-4 {
  margin-top: 0.5rem;
}

.compact-mode .mt-6 {
  margin-top: 0.75rem;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  @apply bg-transparent;
}

::-webkit-scrollbar-thumb {
  @apply bg-muted-foreground/30 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-muted-foreground/50;
}

/* Sidebar scrollbar */
.sidebar::-webkit-scrollbar {
  width: 4px;
}

.sidebar::-webkit-scrollbar-thumb {
  @apply bg-white/30 rounded-full;
}

.sidebar::-webkit-scrollbar-thumb:hover {
  @apply bg-white/50;
}

/* Page transitions */
.page-transition-enter {
  opacity: 0;
  transform: scale(0.98);
}

.page-transition-enter-active {
  opacity: 1;
  transform: scale(1);
  transition: opacity 300ms, transform 300ms;
}

.page-transition-exit {
  opacity: 1;
  transform: scale(1);
}

.page-transition-exit-active {
  opacity: 0;
  transform: scale(0.98);
  transition: opacity 300ms, transform 300ms;
}

/* Print styles */
@media print {
  body {
    background: white;
    font-size: 12pt;
    color: black;
  }

  .no-print, .no-print * {
    display: none !important;
  }

  .print-container {
    width: 100%;
    margin: 0;
    padding: 0;
  }

  table {
    width: 100%;
    border-collapse: collapse;
    page-break-inside: auto;
  }

  tr {
    page-break-inside: avoid;
    page-break-after: auto;
  }

  th, td {
    padding: 8px;
    text-align: left;
    border: 1px solid #ddd;
  }

  th {
    background-color: #f2f2f2;
    font-weight: bold;
  }

  h1, h2, h3, h4, h5, h6 {
    page-break-after: avoid;
  }

  img, svg {
    max-width: 100% !important;
  }

  p, h2, h3 {
    orphans: 3;
    widows: 3;
  }

  thead {
    display: table-header-group;
  }

  tfoot {
    display: table-footer-group;
  }
}

/* PDF Export Styles */
.pdf-export-mode {
  background: white;
  padding: 20px;
}

.pdf-export-mode table {
  border-collapse: collapse;
  width: 100%;
  border-spacing: 0;
  margin-bottom: 20px;
}

.pdf-export-mode tr {
  page-break-inside: avoid !important;
  break-inside: avoid !important;
  display: table-row;
}

.pdf-export-mode th,
.pdf-export-mode td {
  padding: 8px;
  border: 1px solid #ddd;
  vertical-align: top;
}

.pdf-export-mode th {
  background-color: #f2f2f2;
  font-weight: bold;
  text-align: left;
}

.pdf-export-mode .no-print {
  display: none !important;
}

/* Ensure table headers repeat on each page */
.pdf-export-mode thead {
  display: table-header-group;
}

.pdf-export-mode tfoot {
  display: table-footer-group;
}

/* Add space between rows */
.pdf-export-mode tr.task-row {
  border-bottom: 2px solid #eee;
  margin-bottom: 10px;
  height: auto;
}

/* Ensure proper spacing between cells */
.pdf-export-mode td {
  padding-top: 8px;
  padding-bottom: 8px;
}

/* Ensure proper page breaks */
@media print {
  .pdf-export-mode tr {
    page-break-inside: avoid !important;
    break-inside: avoid !important;
  }

  .pdf-export-mode thead {
    display: table-header-group;
  }

  .pdf-export-mode tfoot {
    display: table-footer-group;
  }
}

/* html2pdf specific styles */
.task-row {
  page-break-inside: avoid !important;
  break-inside: avoid !important;
}

/* Add extra space after each row to prevent content from being cut off */
.task-row td {
  padding-bottom: 12px !important;
}

/* Ensure table headers repeat on each page for html2pdf */
thead {
  display: table-header-group;
}

tfoot {
  display: table-footer-group;
}

/* Add extra padding at the bottom of each page */
@page {
  margin-bottom: 20mm;
}

/* Styles for when PDF is being generated */
.generating-pdf .print-content {
  padding: 30px !important;
}

.generating-pdf table {
  border-collapse: collapse !important;
  width: 100% !important;
}

.generating-pdf tr {
  page-break-inside: avoid !important;
  break-inside: avoid !important;
}

.generating-pdf td {
  padding: 10px !important;
  vertical-align: top !important;
  border: 1px solid #ddd !important;
}

.generating-pdf th {
  padding: 10px !important;
  background-color: #f2f2f2 !important;
  border: 1px solid #ddd !important;
}


