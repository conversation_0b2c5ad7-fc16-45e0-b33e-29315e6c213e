/**
 * Final test for assignee functionality with enhanced prompt
 */

async function finalAssigneeTest() {
  console.log('🎯 Final Assignee Test - Enhanced Prompt');
  console.log('=' .repeat(50));
  
  const command = 'There is a broken window at Thames, assign to <PERSON><PERSON> and make it due on March 23rd';
  const userId = 'c749ea63-c8cb-4e8b-b428-9a467755408b';
  
  console.log(`🤖 Command: "${command}"`);
  console.log(`👤 User: <PERSON> (${userId})`);
  console.log('');
  console.log('🔧 Enhanced prompt includes:');
  console.log('   ✅ Explicit JSON example with assignee field');
  console.log('   ✅ CRITICAL instruction about assignee extraction');
  console.log('   ✅ MUST include assignee field requirement');
  console.log('   ✅ Debug logging to see AI response');
  
  try {
    console.log('\n📡 Sending request...');
    
    const response = await fetch('/functions/v1/ai-command-processor', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('supabase.auth.token')}`,
      },
      body: JSON.stringify({
        command: command,
        userId: userId
      })
    });

    const result = await response.json();
    
    console.log('\n📋 Response:');
    console.log(JSON.stringify(result, null, 2));
    
    if (result.success) {
      console.log('\n✅ Task created successfully');
      
      // Check for assignee in message
      if (result.message.includes('assigned to Ashlee Glass')) {
        console.log('🎉 SUCCESS! Assignee correctly processed!');
        console.log('✅ Message mentions "assigned to Ashlee Glass"');
        
        if (result.entityId) {
          console.log(`📋 Task ID: ${result.entityId}`);
          console.log('\n🔍 Expected database values:');
          console.log('   assigned_to = "Ashlee Glass"');
          console.log('   provider_id = "4a72122e-c35f-4a51-ae6f-3f085248fc39"');
          
          // Test the database query
          await verifyTaskInDatabase(result.entityId);
        }
        
        return { success: true, working: true, taskId: result.entityId };
        
      } else if (result.message.includes('assigned to')) {
        console.log('⚠️  Partial success - assignee processed but name might be different');
        console.log(`   Message: ${result.message}`);
        return { success: true, working: 'partial', taskId: result.entityId };
        
      } else {
        console.log('❌ Task created but NO assignee mentioned in message');
        console.log('💡 The AI is still not extracting the assignee field');
        return { success: true, working: false, taskId: result.entityId };
      }
      
    } else {
      console.log('\n❌ Task creation failed');
      console.log(`Error: ${result.message}`);
      return { success: false, working: false, error: result.message };
    }
    
  } catch (error) {
    console.error('\n💥 Request failed:', error);
    return { success: false, working: false, error: error.message };
  }
}

async function verifyTaskInDatabase(taskId) {
  console.log('\n🗄️  Database Verification');
  console.log('-' .repeat(30));
  
  console.log('💡 To verify the task was created correctly, run this SQL:');
  console.log(`SELECT id, title, assigned_to, provider_id, property_name, due_date`);
  console.log(`FROM maintenance_tasks WHERE id = '${taskId}';`);
  
  console.log('\n🎯 Expected results:');
  console.log('   assigned_to: "Ashlee Glass" (not null)');
  console.log('   provider_id: "4a72122e-c35f-4a51-ae6f-3f085248fc39" (not null)');
  console.log('   title: "Fix broken window"');
  console.log('   property_name: contains "Thames"');
  console.log('   due_date: "2025-03-23"');
}

// Test multiple variations
async function testMultipleVariations() {
  console.log('🧪 Testing Multiple Assignee Variations');
  console.log('=' .repeat(50));
  
  const variations = [
    'Fix the door, assign to Ashlee Glass',
    'Repair the faucet, assign it to Ashlee',
    'Schedule maintenance, assign to Glass',
    'Clean the pool, assign to Ashlee Glass'
  ];
  
  const userId = 'c749ea63-c8cb-4e8b-b428-9a467755408b';
  const results = [];
  
  for (let i = 0; i < variations.length; i++) {
    const command = variations[i];
    
    console.log(`\n📝 Test ${i + 1}/${variations.length}: "${command}"`);
    console.log('-' .repeat(40));
    
    try {
      const response = await fetch('/functions/v1/ai-command-processor', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('supabase.auth.token')}`,
        },
        body: JSON.stringify({
          command: command,
          userId: userId
        })
      });

      const result = await response.json();
      
      if (result.success) {
        const hasAssignee = result.message.includes('assigned to');
        console.log(`${hasAssignee ? '✅' : '❌'} ${hasAssignee ? 'SUCCESS' : 'FAILED'}: ${result.message}`);
        results.push({ command, success: true, hasAssignee, message: result.message });
      } else {
        console.log(`❌ FAILED: ${result.message}`);
        results.push({ command, success: false, hasAssignee: false, error: result.message });
      }
      
      // Wait between tests
      await new Promise(resolve => setTimeout(resolve, 1500));
      
    } catch (error) {
      console.error(`💥 Error: ${error.message}`);
      results.push({ command, success: false, hasAssignee: false, error: error.message });
    }
  }
  
  // Summary
  console.log('\n' + '🎯 SUMMARY'.padStart(30, '=').padEnd(50, '='));
  const successCount = results.filter(r => r.success && r.hasAssignee).length;
  console.log(`📊 Assignee working: ${successCount}/${variations.length} tests`);
  
  results.forEach((result, index) => {
    const status = result.success && result.hasAssignee ? '✅' : '❌';
    console.log(`${status} Test ${index + 1}: ${result.success && result.hasAssignee ? 'WORKING' : 'NOT WORKING'}`);
  });
  
  if (successCount === variations.length) {
    console.log('\n🎉 ALL TESTS PASSED! Assignee functionality is fully working!');
  } else if (successCount > 0) {
    console.log('\n⚠️  PARTIAL SUCCESS - Some assignee commands work');
  } else {
    console.log('\n❌ ALL TESTS FAILED - Assignee functionality not working');
  }
  
  return results;
}

// Quick test
async function quickTest() {
  console.log('⚡ Quick Assignee Test');
  console.log('=' .repeat(25));
  
  const result = await finalAssigneeTest();
  
  if (result.working === true) {
    console.log('\n🎉 QUICK TEST PASSED!');
    console.log('✅ Assignee functionality is working correctly!');
  } else if (result.working === 'partial') {
    console.log('\n⚠️  PARTIAL SUCCESS');
    console.log('✅ Task created with some assignee processing');
  } else {
    console.log('\n❌ QUICK TEST FAILED');
    console.log('💡 Assignee functionality still not working');
  }
  
  return result;
}

// Export functions
window.finalAssigneeTest = finalAssigneeTest;
window.testMultipleVariations = testMultipleVariations;
window.quickTest = quickTest;

console.log(`
🎯 Final Assignee Test Suite

Enhanced with:
• More explicit AI prompt
• CRITICAL assignee extraction instruction
• Example JSON response format
• Debug logging

Available functions:
• finalAssigneeTest() - Test the main command (recommended)
• quickTest() - Quick single test
• testMultipleVariations() - Test multiple assignee patterns

Example usage:
finalAssigneeTest()
`);

// Auto-run final test
console.log('🔧 Auto-running final test in 2 seconds...');
setTimeout(() => {
  quickTest();
}, 2000);
