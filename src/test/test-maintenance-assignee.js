/**
 * Test script for AI maintenance task assignee functionality
 * 
 * This script tests the newly deployed assignee feature for maintenance tasks.
 * 
 * To use:
 * 1. Open StayFu app in browser
 * 2. Make sure you're logged in
 * 3. Open browser console (F12)
 * 4. Copy and paste this script
 * 5. Run: testMaintenanceAssignee()
 */

// Test configuration
const ASSIGNEE_TEST_CONFIG = {
  // Known test user who can create tasks
  testUserId: 'e4416a70-7490-4c40-a1c4-a5a6aeadf6ea',
  
  // Known assignee
  knownAssignee: {
    id: '4a72122e-c35f-4a51-ae6f-3f085248fc39',
    name: '<PERSON><PERSON>',
    email: '<EMAIL>',
    role: 'service_provider'
  },
  
  // Test commands
  testCommands: [
    'There is a broken window at Thames, assign to Ashlee Glass and make it due on March 23rd',
    'Fix the leaky faucet, assign it to <PERSON><PERSON>',
    'Schedule HVAC maintenance for next week, assign to <PERSON><PERSON>',
    'Repair the broken door handle at Thames, assign to <PERSON><PERSON>'
  ]
};

// Get current user ID
function getCurrentUserId() {
  try {
    const authData = localStorage.getItem('supabase.auth.token');
    if (authData) {
      const parsed = JSON.parse(authData);
      return parsed.user?.id;
    }
  } catch (e) {
    try {
      const user = JSON.parse(localStorage.getItem('supabase.auth.user') || '{}');
      return user.id;
    } catch (e2) {
      return null;
    }
  }
  return null;
}

// Test a single AI command
async function testAICommand(command, userId = null) {
  const testUserId = userId || getCurrentUserId() || ASSIGNEE_TEST_CONFIG.testUserId;
  
  console.log(`🤖 Testing: "${command}"`);
  console.log(`👤 User ID: ${testUserId}`);
  
  try {
    const response = await fetch('/functions/v1/ai-command-processor', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('supabase.auth.token')}`,
      },
      body: JSON.stringify({
        command: command,
        userId: testUserId
      })
    });

    const result = await response.json();
    
    console.log('📋 Result:');
    console.log(JSON.stringify(result, null, 2));
    
    if (result.success) {
      console.log('✅ SUCCESS!');
      console.log(`🎉 ${result.message}`);
      
      if (result.entityType === 'maintenance_task') {
        console.log(`📋 Created maintenance task ID: ${result.entityId}`);
        
        // Check if assignee was mentioned in the message
        if (result.message.includes('assigned to')) {
          console.log('✅ Assignee was successfully processed!');
        } else {
          console.log('⚠️  Assignee might not have been processed');
        }
      }
      
      return { success: true, result };
      
    } else {
      console.log('❌ FAILED');
      console.log(`💬 ${result.message}`);
      return { success: false, result };
    }
    
  } catch (error) {
    console.error('💥 Error:', error);
    return { success: false, error: error.message };
  }
}

// Verify a created task has the correct assignee
async function verifyTaskAssignee(taskId) {
  console.log(`🔍 Verifying task ${taskId} assignee...`);
  
  // This would require access to Supabase client
  // For now, just log that we should check manually
  console.log('💡 To verify assignee, check the maintenance task in the UI or database');
  console.log(`   Task ID: ${taskId}`);
  console.log(`   Expected assignee: ${ASSIGNEE_TEST_CONFIG.knownAssignee.name}`);
}

// Test all assignee commands
async function testMaintenanceAssignee() {
  console.log('🚀 Testing AI Maintenance Task Assignee Functionality');
  console.log('=' .repeat(60));
  
  const results = [];
  
  for (let i = 0; i < ASSIGNEE_TEST_CONFIG.testCommands.length; i++) {
    const command = ASSIGNEE_TEST_CONFIG.testCommands[i];
    
    console.log(`\n📝 Test ${i + 1}/${ASSIGNEE_TEST_CONFIG.testCommands.length}`);
    console.log('-' .repeat(40));
    
    const result = await testAICommand(command);
    results.push({ command, ...result });
    
    // Wait a bit between tests
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  // Summary
  console.log('\n' + '🎯 TEST SUMMARY'.padStart(40, '=').padEnd(60, '='));
  
  const successCount = results.filter(r => r.success).length;
  const totalTests = results.length;
  
  console.log(`📊 Overall: ${successCount}/${totalTests} tests passed`);
  
  results.forEach((result, index) => {
    const status = result.success ? '✅' : '❌';
    console.log(`${status} Test ${index + 1}: ${result.success ? 'PASS' : 'FAIL'}`);
    if (result.success && result.result?.message) {
      console.log(`   Message: ${result.result.message}`);
    }
  });
  
  if (successCount === totalTests) {
    console.log('\n🎉 ALL TESTS PASSED!');
    console.log('✅ AI maintenance task assignee functionality is working correctly');
    console.log('✅ Commands like "assign to Ashlee Glass" are being processed');
    console.log('✅ Assignee information is included in success messages');
  } else {
    console.log('\n⚠️  SOME TESTS FAILED');
    console.log('💡 Check the individual test results above for details');
  }
  
  return results;
}

// Test with current user
async function testWithCurrentUser() {
  const currentUserId = getCurrentUserId();
  if (!currentUserId) {
    console.log('❌ Could not detect current user ID. Make sure you are logged in.');
    return;
  }
  
  console.log('🧪 Testing with your current user account...');
  console.log(`👤 Your user ID: ${currentUserId}`);
  
  const testCommand = 'Fix the broken light switch, assign to Ashlee Glass';
  return await testAICommand(testCommand, currentUserId);
}

// Quick test with a simple command
async function quickAssigneeTest() {
  console.log('⚡ Quick Assignee Test');
  console.log('=' .repeat(30));
  
  const command = 'There is a broken window at Thames, assign to Ashlee Glass and make it due on March 23rd';
  const result = await testAICommand(command);
  
  if (result.success) {
    console.log('\n🎉 QUICK TEST PASSED!');
    console.log('✅ The assignee functionality is working');
  } else {
    console.log('\n❌ QUICK TEST FAILED');
    console.log('💡 Check the error message above');
  }
  
  return result;
}

// Export functions
window.testMaintenanceAssignee = testMaintenanceAssignee;
window.testWithCurrentUser = testWithCurrentUser;
window.quickAssigneeTest = quickAssigneeTest;
window.testAICommand = testAICommand;

console.log(`
🧪 AI Maintenance Task Assignee Test Suite

Available functions:
• testMaintenanceAssignee() - Run all assignee tests (recommended)
• quickAssigneeTest() - Quick test with one command
• testWithCurrentUser() - Test with your current account
• testAICommand("your command here") - Test a specific command

Example usage:
testMaintenanceAssignee()

Known assignee for testing: ${ASSIGNEE_TEST_CONFIG.knownAssignee.name}
`);

// Auto-run quick test
console.log('🔧 Auto-running quick test in 3 seconds...');
setTimeout(() => {
  quickAssigneeTest();
}, 3000);
