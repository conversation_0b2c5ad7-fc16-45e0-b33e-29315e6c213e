/**
 * Test the dual approach: AI extraction + regex fallback
 */

async function testDualApproach() {
  console.log('🔧 Testing Dual Approach: AI + Regex Fallback');
  console.log('=' .repeat(50));
  
  const command = 'broken door handle at c st, assign to <PERSON><PERSON> glass';
  const userId = 'c749ea63-c8cb-4e8b-b428-9a467755408b';
  
  console.log(`🤖 Command: "${command}"`);
  console.log(`👤 User: <PERSON> (${userId})`);
  console.log('');
  console.log('🔧 Dual approach includes:');
  console.log('   1️⃣ Enhanced AI prompt with explicit examples');
  console.log('   2️⃣ Regex fallback: /assign\\s+(?:to|it\\s+to)\\s+([^,\\s]+(?:\\s+[^,\\s]+)*)/i');
  console.log('   3️⃣ Post-processing to extract assignee if AI missed it');
  console.log('   4️⃣ Debug logging to see both AI and regex results');
  
  try {
    console.log('\n📡 Sending request...');
    
    const response = await fetch('/functions/v1/ai-command-processor', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('supabase.auth.token')}`,
      },
      body: JSON.stringify({
        command: command,
        userId: userId
      })
    });

    const result = await response.json();
    
    console.log('\n📋 Response:');
    console.log(JSON.stringify(result, null, 2));
    
    if (result.success) {
      console.log('\n✅ Task created successfully');
      
      // Check for assignee in message
      if (result.message.includes('assigned to Ashlee glass') || result.message.includes('assigned to Ashlee Glass')) {
        console.log('🎉 SUCCESS! Assignee correctly processed!');
        console.log('✅ Message mentions assignee assignment');
        
        if (result.entityId) {
          console.log(`📋 Task ID: ${result.entityId}`);
          
          // Verify in database
          await verifyTaskAssignee(result.entityId);
        }
        
        return { success: true, working: true, taskId: result.entityId };
        
      } else if (result.message.includes('assigned to')) {
        console.log('⚠️  Partial success - assignee processed but name might be different');
        console.log(`   Message: ${result.message}`);
        return { success: true, working: 'partial', taskId: result.entityId };
        
      } else {
        console.log('❌ Task created but NO assignee mentioned in message');
        console.log('💡 Both AI and regex extraction failed');
        
        if (result.entityId) {
          await verifyTaskAssignee(result.entityId);
        }
        
        return { success: true, working: false, taskId: result.entityId };
      }
      
    } else {
      console.log('\n❌ Task creation failed');
      console.log(`Error: ${result.message}`);
      return { success: false, working: false, error: result.message };
    }
    
  } catch (error) {
    console.error('\n💥 Request failed:', error);
    return { success: false, working: false, error: error.message };
  }
}

async function verifyTaskAssignee(taskId) {
  console.log('\n🔍 Database Verification');
  console.log('-' .repeat(30));
  
  console.log('💡 To verify the task assignee, run this SQL:');
  console.log(`SELECT id, title, assigned_to, provider_id FROM maintenance_tasks WHERE id = '${taskId}';`);
  
  console.log('\n🎯 Expected if working:');
  console.log('   assigned_to: "Ashlee glass" or "Ashlee Glass" (not null)');
  console.log('   provider_id: "4a72122e-c35f-4a51-ae6f-3f085248fc39" (not null)');
  
  console.log('\n❌ If still broken:');
  console.log('   assigned_to: null');
  console.log('   provider_id: null');
}

// Test the regex pattern directly
function testRegexPattern() {
  console.log('🔍 Testing Regex Pattern');
  console.log('=' .repeat(30));
  
  const pattern = /assign\s+(?:to|it\s+to)\s+([^,\s]+(?:\s+[^,\s]+)*)/i;
  
  const testCases = [
    'broken door handle at c st, assign to Ashlee glass',
    'Fix the door, assign to Ashlee Glass',
    'Repair the faucet, assign it to Ashlee',
    'Schedule maintenance, assign to Glass',
    'Clean the pool, assign to John Smith'
  ];
  
  console.log('📝 Testing regex pattern:');
  console.log(`   ${pattern}`);
  
  testCases.forEach((testCase, index) => {
    const match = testCase.match(pattern);
    if (match) {
      console.log(`✅ Test ${index + 1}: "${testCase}"`);
      console.log(`   → Extracted: "${match[1]}"`);
    } else {
      console.log(`❌ Test ${index + 1}: "${testCase}"`);
      console.log(`   → No match found`);
    }
  });
  
  return pattern;
}

// Test multiple commands with the dual approach
async function testMultipleCommands() {
  console.log('🧪 Testing Multiple Commands with Dual Approach');
  console.log('=' .repeat(50));
  
  const commands = [
    'broken door handle at c st, assign to Ashlee glass',
    'Fix the window, assign to Ashlee Glass',
    'Repair the faucet, assign it to Ashlee',
    'Schedule maintenance, assign to Glass'
  ];
  
  const userId = 'c749ea63-c8cb-4e8b-b428-9a467755408b';
  const results = [];
  
  for (let i = 0; i < commands.length; i++) {
    const command = commands[i];
    
    console.log(`\n📝 Test ${i + 1}/${commands.length}: "${command}"`);
    console.log('-' .repeat(40));
    
    try {
      const response = await fetch('/functions/v1/ai-command-processor', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('supabase.auth.token')}`,
        },
        body: JSON.stringify({
          command: command,
          userId: userId
        })
      });

      const result = await response.json();
      
      if (result.success) {
        const hasAssignee = result.message.includes('assigned to');
        console.log(`${hasAssignee ? '✅' : '❌'} ${hasAssignee ? 'SUCCESS' : 'FAILED'}: ${result.message}`);
        results.push({ command, success: true, hasAssignee, message: result.message, taskId: result.entityId });
      } else {
        console.log(`❌ FAILED: ${result.message}`);
        results.push({ command, success: false, hasAssignee: false, error: result.message });
      }
      
      // Wait between tests
      await new Promise(resolve => setTimeout(resolve, 2000));
      
    } catch (error) {
      console.error(`💥 Error: ${error.message}`);
      results.push({ command, success: false, hasAssignee: false, error: error.message });
    }
  }
  
  // Summary
  console.log('\n' + '🎯 SUMMARY'.padStart(30, '=').padEnd(50, '='));
  const successCount = results.filter(r => r.success && r.hasAssignee).length;
  console.log(`📊 Assignee working: ${successCount}/${commands.length} tests`);
  
  results.forEach((result, index) => {
    const status = result.success && result.hasAssignee ? '✅' : '❌';
    console.log(`${status} Test ${index + 1}: ${result.success && result.hasAssignee ? 'WORKING' : 'NOT WORKING'}`);
  });
  
  if (successCount === commands.length) {
    console.log('\n🎉 ALL TESTS PASSED! Dual approach is working!');
  } else if (successCount > 0) {
    console.log('\n⚠️  PARTIAL SUCCESS - Some commands work');
  } else {
    console.log('\n❌ ALL TESTS FAILED - Dual approach not working');
  }
  
  return results;
}

// Export functions
window.testDualApproach = testDualApproach;
window.testRegexPattern = testRegexPattern;
window.testMultipleCommands = testMultipleCommands;

console.log(`
🔧 Dual Approach Test Suite

Features:
• Enhanced AI prompt with explicit examples
• Regex fallback extraction
• Post-processing if AI misses assignee
• Debug logging for both approaches

Available functions:
• testDualApproach() - Test the main command (recommended)
• testRegexPattern() - Test regex extraction only
• testMultipleCommands() - Test multiple variations

Example usage:
testDualApproach()
`);

// Auto-run dual approach test
console.log('🔧 Auto-running dual approach test in 2 seconds...');
setTimeout(() => {
  testDualApproach();
}, 2000);
