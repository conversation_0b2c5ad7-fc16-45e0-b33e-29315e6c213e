/**
 * Quick Test for Deployed AI Purchase Order Fix
 *
 * This script quickly tests the deployed fix to verify it works.
 *
 * To use:
 * 1. Open StayFu app in browser (http://localhost:8081 or deployed version)
 * 2. Make sure you're logged in
 * 3. Open browser console (F12)
 * 4. Copy and paste this entire script
 * 5. Run: quickTestDeployedFix()
 */

// Quick test function
async function quickTestDeployedFix() {
  console.log('🚀 Quick Test: AI Purchase Order Fix');
  console.log('=' .repeat(50));

  // Test with known user that has low stock items
  const testUserId = 'e4416a70-7490-4c40-a1c4-a5a6aeadf6ea';

  try {
    console.log('🤖 Testing AI command: "Create a purchase order for all low stock items"');
    console.log(`👤 Using test user: ${testUserId}`);

    const response = await fetch('/functions/v1/ai-command-processor', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('supabase.auth.token')}`,
      },
      body: JSON.stringify({
        command: 'Create a purchase order for all low stock items',
        userId: testUserId
      })
    });

    const result = await response.json();

    console.log('\n📋 Result:');
    console.log(JSON.stringify(result, null, 2));

    if (result.success) {
      console.log('\n✅ SUCCESS! The fix is working correctly!');
      console.log(`🎉 ${result.message}`);

      if (result.entityType === 'purchase_order') {
        console.log(`📋 Created purchase order ID: ${result.entityId}`);
      }

      console.log('\n🎯 What this means:');
      console.log('• The RPC function is working correctly');
      console.log('• The Edge Function deployment was successful');
      console.log('• AI purchase order creation is now functional');
      console.log('• The "No valid items found" error is fixed');

    } else {
      console.log('\n❌ Test failed, but this might be expected:');
      console.log(`💬 Message: ${result.message}`);

      if (result.message.includes('No low stock items found')) {
        console.log('\n💡 This is actually good news!');
        console.log('• The Edge Function is working correctly');
        console.log('• The RPC function is being called properly');
        console.log('• The error message is now helpful and descriptive');
        console.log('• The fix is deployed and functional');
        console.log('\n🔍 The test user might not have low stock items anymore,');
        console.log('   or they might have been restocked. This is normal!');
      } else {
        console.log('\n🔍 Unexpected error. Check the message above for details.');
      }
    }

  } catch (error) {
    console.error('\n💥 Error during test:', error);
    console.log('\n💡 This might mean:');
    console.log('• You need to be logged in to the StayFu app');
    console.log('• The Edge Function URL might be different');
    console.log('• There might be a network issue');
  }

  console.log('\n' + '=' .repeat(50));
  console.log('🏁 Quick test complete!');
}

// Test with current user
async function testWithCurrentUser() {
  console.log('🧪 Testing with your current user account...');

  // Try to get current user ID
  let currentUserId = null;
  try {
    const authData = localStorage.getItem('supabase.auth.token');
    if (authData) {
      const parsed = JSON.parse(authData);
      currentUserId = parsed.user?.id;
    }
  } catch (e) {
    console.log('Could not get current user ID from localStorage');
  }

  if (!currentUserId) {
    console.log('❌ Could not detect your user ID. Make sure you are logged in.');
    return;
  }

  console.log(`👤 Your user ID: ${currentUserId}`);

  try {
    const response = await fetch('/functions/v1/ai-command-processor', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('supabase.auth.token')}`,
      },
      body: JSON.stringify({
        command: 'Create a purchase order for all low stock items',
        userId: currentUserId
      })
    });

    const result = await response.json();

    console.log('\n📋 Result for your account:');
    console.log(JSON.stringify(result, null, 2));

    if (result.success) {
      console.log('\n✅ SUCCESS! Created purchase order for your account!');
    } else {
      console.log(`\n💬 ${result.message}`);
      if (result.message.includes('No low stock items found')) {
        console.log('💡 This means you don\'t have any low stock items, which is good!');
        console.log('   The AI is working correctly.');
      }
    }

  } catch (error) {
    console.error('💥 Error:', error);
  }
}

// Export functions
window.quickTestDeployedFix = quickTestDeployedFix;
window.testWithCurrentUser = testWithCurrentUser;

console.log(`
🧪 Quick Test for Deployed AI Purchase Order Fix

Available functions:
• quickTestDeployedFix() - Test with known low stock data
• testWithCurrentUser() - Test with your current account

Example usage:
quickTestDeployedFix()
`);

// Auto-run the test
console.log('🔧 Auto-running quick test in 3 seconds...');
setTimeout(() => {
  quickTestDeployedFix();
}, 3000);