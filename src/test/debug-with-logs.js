/**
 * Debug test with the new logging to see what the AI is extracting
 */

async function debugWithLogs() {
  console.log('🔍 Debug Test with Edge Function Logs');
  console.log('=' .repeat(50));
  
  const command = 'There is a broken window at Thames, assign to <PERSON><PERSON> Glass and make it due on March 23rd';
  const userId = 'c749ea63-c8cb-4e8b-b428-9a467755408b'; // <PERSON>
  
  console.log(`🤖 Command: "${command}"`);
  console.log(`👤 User: <PERSON> (${userId})`);
  console.log('');
  console.log('🔍 This test will show debug logs from the Edge Function');
  console.log('   Look for:');
  console.log('   - "DEBUG: AI Parsed response" - what the AI extracted');
  console.log('   - "DEBUG: Maintenance task data received" - data passed to handler');
  console.log('   - "DEBUG: Looking for assignee" - if assignee field exists');
  console.log('   - "DEBUG: No assignee field in data" - if assignee missing');
  
  try {
    console.log('\n📡 Sending request...');
    
    const response = await fetch('/functions/v1/ai-command-processor', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('supabase.auth.token')}`,
      },
      body: JSON.stringify({
        command: command,
        userId: userId
      })
    });

    const result = await response.json();
    
    console.log('\n📋 Response:');
    console.log(JSON.stringify(result, null, 2));
    
    if (result.success) {
      console.log('\n✅ Task created successfully');
      
      if (result.message.includes('assigned to')) {
        console.log('✅ SUCCESS! Message mentions assignment');
        console.log('🎉 The assignee functionality is working!');
      } else {
        console.log('❌ Message does NOT mention assignment');
        console.log('💡 Check the Edge Function logs for debug info');
      }
      
      if (result.entityId) {
        console.log(`📋 Task ID: ${result.entityId}`);
        
        // Suggest checking the database
        console.log('\n🔍 To verify in database:');
        console.log(`SELECT id, title, assigned_to, provider_id FROM maintenance_tasks WHERE id = '${result.entityId}';`);
      }
      
    } else {
      console.log('\n❌ Task creation failed');
      console.log(`Error: ${result.message}`);
    }
    
    console.log('\n💡 Next steps:');
    console.log('1. Check the Supabase Edge Function logs in the dashboard');
    console.log('2. Look for the DEBUG messages to see what the AI extracted');
    console.log('3. Verify if the assignee field is present in the AI response');
    
    return result;
    
  } catch (error) {
    console.error('\n💥 Request failed:', error);
    return null;
  }
}

// Test with a simpler command
async function testSimpleAssignment() {
  console.log('🧪 Simple Assignment Test');
  console.log('=' .repeat(30));
  
  const command = 'Fix the door, assign to Ashlee';
  const userId = 'c749ea63-c8cb-4e8b-b428-9a467755408b';
  
  console.log(`Command: "${command}"`);
  
  try {
    const response = await fetch('/functions/v1/ai-command-processor', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('supabase.auth.token')}`,
      },
      body: JSON.stringify({
        command: command,
        userId: userId
      })
    });

    const result = await response.json();
    
    console.log('\n📋 Result:');
    console.log(JSON.stringify(result, null, 2));
    
    return result;
    
  } catch (error) {
    console.error('💥 Error:', error);
    return null;
  }
}

// Check Edge Function logs (manual instruction)
function checkEdgeFunctionLogs() {
  console.log('📊 How to Check Edge Function Logs');
  console.log('=' .repeat(40));
  
  console.log('1. Go to Supabase Dashboard:');
  console.log('   https://supabase.com/dashboard/project/pwaeknalhosfwuxkpaet');
  
  console.log('\n2. Navigate to Edge Functions:');
  console.log('   - Click "Edge Functions" in the sidebar');
  console.log('   - Click on "ai-command-processor"');
  console.log('   - Click on "Logs" tab');
  
  console.log('\n3. Look for recent DEBUG messages:');
  console.log('   - "DEBUG: AI Parsed response" - shows what AI extracted');
  console.log('   - "DEBUG: Maintenance task data received" - shows data structure');
  console.log('   - "DEBUG: Looking for assignee" - confirms assignee processing');
  console.log('   - "DEBUG: No assignee field in data" - indicates missing assignee');
  
  console.log('\n4. Expected debug output:');
  console.log('   If working correctly, you should see:');
  console.log('   - AI response includes "assignee": "Ashlee Glass"');
  console.log('   - "DEBUG: Looking for assignee: Ashlee Glass"');
  console.log('   - "Found assignee: Ashlee Glass"');
  
  console.log('\n5. If not working:');
  console.log('   - AI response missing "assignee" field');
  console.log('   - "DEBUG: No assignee field in data"');
  console.log('   - Need to fix AI prompt or response parsing');
}

// Export functions
window.debugWithLogs = debugWithLogs;
window.testSimpleAssignment = testSimpleAssignment;
window.checkEdgeFunctionLogs = checkEdgeFunctionLogs;

console.log(`
🔍 Debug Test with Edge Function Logs

Available functions:
• debugWithLogs() - Test with full debug logging (recommended)
• testSimpleAssignment() - Test simpler command
• checkEdgeFunctionLogs() - Instructions for checking logs

Example usage:
debugWithLogs()

After running the test, check the Edge Function logs in Supabase Dashboard
to see what the AI is actually extracting.
`);

// Auto-run debug test
console.log('🔧 Auto-running debug test in 2 seconds...');
setTimeout(() => {
  debugWithLogs();
}, 2000);
