/**
 * Test script for the FIXED assignee functionality
 * 
 * This tests the corrected assignee assignment where:
 * - assigned_to field expects TEXT (name), not UUID
 * - provider_id field expects UUID for service providers
 */

async function testFixedAssignee() {
  console.log('🔧 Testing FIXED Assignee Functionality');
  console.log('=' .repeat(50));
  
  const testCommand = 'There is a broken window at Thames, assign to Ashlee Glass and make it due on March 23rd';
  const testUserId = 'e4416a70-7490-4c40-a1c4-a5a6aeadf6ea';
  
  console.log(`🤖 Command: "${testCommand}"`);
  console.log(`👤 User ID: ${testUserId}`);
  
  try {
    const response = await fetch('/functions/v1/ai-command-processor', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('supabase.auth.token')}`,
      },
      body: JSON.stringify({
        command: testCommand,
        userId: testUserId
      })
    });

    const result = await response.json();
    
    console.log('\n📋 AI Response:');
    console.log(JSON.stringify(result, null, 2));
    
    if (result.success) {
      console.log('\n✅ SUCCESS!');
      console.log(`🎉 ${result.message}`);
      
      // Check if assignee is mentioned in the message
      if (result.message.includes('assigned to Ashlee Glass')) {
        console.log('✅ Assignee correctly processed in message!');
      } else if (result.message.includes('assigned to')) {
        console.log('⚠️  Assignee processed but name might be different');
      } else {
        console.log('❌ No assignee mentioned in message');
      }
      
      if (result.entityId) {
        console.log(`📋 Task ID: ${result.entityId}`);
        console.log('\n🔍 Expected database values:');
        console.log('   assigned_to = "Ashlee Glass" (TEXT)');
        console.log('   provider_id = "4a72122e-c35f-4a51-ae6f-3f085248fc39" (UUID)');
        console.log('   title = "Fix broken window"');
        console.log('   property_name contains "Thames"');
        console.log('   due_date = "2025-03-23"');
        
        // Suggest verification query
        console.log('\n💡 To verify in database, run:');
        console.log(`   SELECT id, title, assigned_to, provider_id, property_name, due_date`);
        console.log(`   FROM maintenance_tasks WHERE id = '${result.entityId}';`);
      }
      
      return { success: true, taskId: result.entityId, message: result.message };
      
    } else {
      console.log('\n❌ FAILED');
      console.log(`💬 Error: ${result.message}`);
      return { success: false, error: result.message };
    }
    
  } catch (error) {
    console.error('\n💥 Request failed:', error);
    return { success: false, error: error.message };
  }
}

// Test with current user
async function testWithCurrentUser() {
  const currentUserId = getCurrentUserId();
  if (!currentUserId) {
    console.log('❌ Could not detect current user ID. Make sure you are logged in.');
    return;
  }
  
  console.log('🧪 Testing with your current user...');
  console.log(`👤 Your user ID: ${currentUserId}`);
  
  const command = 'Fix the broken light switch, assign to Ashlee Glass';
  
  try {
    const response = await fetch('/functions/v1/ai-command-processor', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('supabase.auth.token')}`,
      },
      body: JSON.stringify({
        command: command,
        userId: currentUserId
      })
    });

    const result = await response.json();
    
    console.log('\n📋 Result:');
    console.log(JSON.stringify(result, null, 2));
    
    if (result.success && result.message.includes('assigned to')) {
      console.log('\n🎉 SUCCESS! Assignee functionality is working with your account!');
    }
    
    return result;
    
  } catch (error) {
    console.error('💥 Error:', error);
    return { success: false, error: error.message };
  }
}

// Get current user ID
function getCurrentUserId() {
  try {
    const authData = localStorage.getItem('supabase.auth.token');
    if (authData) {
      const parsed = JSON.parse(authData);
      return parsed.user?.id;
    }
  } catch (e) {
    try {
      const user = JSON.parse(localStorage.getItem('supabase.auth.user') || '{}');
      return user.id;
    } catch (e2) {
      return null;
    }
  }
  return null;
}

// Quick verification of the fix
async function quickVerifyFix() {
  console.log('⚡ Quick Fix Verification');
  console.log('=' .repeat(30));
  
  console.log('🔧 Key fixes applied:');
  console.log('✅ assigned_to field now uses TEXT (name) instead of UUID');
  console.log('✅ provider_id field set for service providers');
  console.log('✅ AI prompt includes explicit assignee field specification');
  console.log('✅ Assignee lookup includes role information');
  
  console.log('\n🧪 Running test...');
  const result = await testFixedAssignee();
  
  if (result.success) {
    console.log('\n🎉 FIX VERIFIED! Assignee functionality is now working!');
  } else {
    console.log('\n❌ Fix verification failed. Check the error above.');
  }
  
  return result;
}

// Export functions
window.testFixedAssignee = testFixedAssignee;
window.testWithCurrentUser = testWithCurrentUser;
window.quickVerifyFix = quickVerifyFix;

console.log(`
🔧 Fixed Assignee Test Suite

The key issue was: assigned_to field expects TEXT (name), not UUID!

Available functions:
• testFixedAssignee() - Test the exact failing command (recommended)
• quickVerifyFix() - Quick verification of the fix
• testWithCurrentUser() - Test with your current account

Example usage:
testFixedAssignee()
`);

// Auto-run verification
console.log('🔧 Auto-running fix verification in 2 seconds...');
setTimeout(() => {
  quickVerifyFix();
}, 2000);
