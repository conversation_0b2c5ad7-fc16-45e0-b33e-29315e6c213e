/**
 * Debug script for assignee issue
 * 
 * This script helps debug why the assignee is not being assigned to maintenance tasks.
 */

// Test the exact command that was used
async function debugAssigneeIssue() {
  console.log('🔍 Debugging Assignee Issue');
  console.log('=' .repeat(50));
  
  const testCommand = 'There is a broken window at Thames, assign to <PERSON><PERSON> Glass and make it due on March 23rd';
  const testUserId = 'e4416a70-7490-4c40-a1c4-a5a6aeadf6ea'; // Known test user
  
  console.log(`🤖 Testing command: "${testCommand}"`);
  console.log(`👤 User ID: ${testUserId}`);
  
  try {
    console.log('\n📡 Sending request to AI command processor...');
    
    const response = await fetch('/functions/v1/ai-command-processor', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('supabase.auth.token')}`,
      },
      body: JSON.stringify({
        command: testCommand,
        userId: testUserId
      })
    });

    const result = await response.json();
    
    console.log('\n📋 AI Response:');
    console.log(JSON.stringify(result, null, 2));
    
    if (result.success) {
      console.log('\n✅ Task created successfully');
      console.log(`📋 Task ID: ${result.entityId}`);
      console.log(`💬 Message: ${result.message}`);
      
      // Check if the message mentions assignment
      if (result.message.includes('assigned to')) {
        console.log('✅ Message indicates assignment was processed');
      } else {
        console.log('❌ Message does NOT mention assignment');
        console.log('💡 This suggests the AI did not extract the assignee');
      }
      
      // Now check the actual database record
      if (result.entityId) {
        await checkTaskInDatabase(result.entityId);
      }
      
    } else {
      console.log('\n❌ Task creation failed');
      console.log(`💬 Error: ${result.message}`);
    }
    
  } catch (error) {
    console.error('\n💥 Request failed:', error);
  }
}

// Check the task in the database
async function checkTaskInDatabase(taskId) {
  console.log(`\n🗄️  Checking task ${taskId} in database...`);
  
  // We can't directly query the database from the browser, but we can suggest what to check
  console.log('💡 To check the database record, run this SQL query:');
  console.log(`   SELECT id, title, assigned_to, property_name, due_date, created_at`);
  console.log(`   FROM maintenance_tasks WHERE id = '${taskId}';`);
  console.log('');
  console.log('🔍 Expected values:');
  console.log('   - assigned_to should be: 4a72122e-c35f-4a51-ae6f-3f085248fc39');
  console.log('   - title should be: "Fix broken window"');
  console.log('   - property_name should contain "Thames"');
  console.log('   - due_date should be: 2025-03-23');
}

// Test just the assignee lookup part
async function testAssigneeLookup() {
  console.log('\n🔍 Testing assignee lookup logic...');
  
  // Simulate what the Edge Function should be doing
  const assigneeName = 'Ashlee Glass';
  
  console.log(`👤 Looking for assignee: "${assigneeName}"`);
  console.log('💡 The Edge Function should be running a query like:');
  console.log(`   SELECT id, first_name, last_name, email FROM profiles`);
  console.log(`   WHERE first_name ILIKE '%${assigneeName}%'`);
  console.log(`      OR last_name ILIKE '%${assigneeName}%'`);
  console.log(`      OR email ILIKE '%${assigneeName}%';`);
  console.log('');
  console.log('🎯 Expected result: Ashlee Glass (4a72122e-c35f-4a51-ae6f-3f085248fc39)');
}

// Test different variations of the assignee name
async function testAssigneeVariations() {
  console.log('\n🧪 Testing different assignee name variations...');
  
  const variations = [
    'Ashlee Glass',
    'Ashlee',
    'Glass',
    'ashlee glass',
    'ASHLEE GLASS'
  ];
  
  for (const variation of variations) {
    console.log(`\n📝 Testing: "${variation}"`);
    const command = `Fix the broken door, assign to ${variation}`;
    
    // We'll just log what should happen, since we can't easily test each variation
    console.log(`   Command: "${command}"`);
    console.log(`   Should find: Ashlee Glass`);
  }
}

// Main debug function
async function runFullDebug() {
  console.log('🚀 Full Assignee Debug Session');
  console.log('=' .repeat(60));
  
  await debugAssigneeIssue();
  await testAssigneeLookup();
  await testAssigneeVariations();
  
  console.log('\n' + '🎯 DEBUG SUMMARY'.padStart(40, '=').padEnd(60, '='));
  console.log('1. Check the AI response message for "assigned to" text');
  console.log('2. Verify the database record has the correct assigned_to UUID');
  console.log('3. Check Edge Function logs for assignee lookup errors');
  console.log('4. Confirm the AI is extracting assignee from the command');
  
  console.log('\n💡 Possible issues:');
  console.log('• AI prompt not extracting assignee correctly');
  console.log('• Assignee lookup query failing');
  console.log('• Database permission issues');
  console.log('• Edge Function deployment issues');
}

// Export functions
window.debugAssigneeIssue = debugAssigneeIssue;
window.checkTaskInDatabase = checkTaskInDatabase;
window.testAssigneeLookup = testAssigneeLookup;
window.runFullDebug = runFullDebug;

console.log(`
🔍 Assignee Debug Tools Ready

Available functions:
• debugAssigneeIssue() - Test the exact failing command
• runFullDebug() - Complete debug session
• testAssigneeLookup() - Check assignee lookup logic

Example usage:
debugAssigneeIssue()
`);

// Auto-run debug
console.log('🔧 Auto-running debug in 2 seconds...');
setTimeout(() => {
  debugAssigneeIssue();
}, 2000);
