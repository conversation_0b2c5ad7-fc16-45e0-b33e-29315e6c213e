/**
 * Debug AI extraction to see what the AI is actually extracting from commands
 */

async function debugAIExtraction() {
  console.log('🔍 Debugging AI Extraction');
  console.log('=' .repeat(50));
  
  const testCommands = [
    'There is a broken window at Thames, assign to <PERSON><PERSON> Glass and make it due on March 23rd',
    'Fix the door, assign to <PERSON><PERSON>',
    'Repair the faucet, assign it to <PERSON><PERSON>',
    'Schedule maintenance, assign to <PERSON>'
  ];
  
  const testUserId = 'c749ea63-c8cb-4e8b-b428-9a467755408b'; // User from recent tasks
  
  for (let i = 0; i < testCommands.length; i++) {
    const command = testCommands[i];
    
    console.log(`\n📝 Test ${i + 1}: "${command}"`);
    console.log('-' .repeat(40));
    
    try {
      const response = await fetch('/functions/v1/ai-command-processor', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('supabase.auth.token')}`,
        },
        body: JSON.stringify({
          command: command,
          userId: testUserId
        })
      });

      const result = await response.json();
      
      console.log('📋 AI Response:');
      console.log(JSON.stringify(result, null, 2));
      
      if (result.success) {
        console.log('✅ Task created successfully');
        
        // Check if assignee is mentioned in the message
        if (result.message.includes('assigned to')) {
          console.log('✅ Message mentions assignment');
        } else {
          console.log('❌ Message does NOT mention assignment');
          console.log('💡 This suggests AI did not extract assignee');
        }
        
        // Check the created task in database
        if (result.entityId) {
          await checkTaskAssignee(result.entityId);
        }
        
      } else {
        console.log('❌ Task creation failed');
        console.log(`Error: ${result.message}`);
      }
      
      // Wait between tests
      await new Promise(resolve => setTimeout(resolve, 2000));
      
    } catch (error) {
      console.error('💥 Error:', error);
    }
  }
}

async function checkTaskAssignee(taskId) {
  console.log(`🔍 Checking task ${taskId} in database...`);
  
  // We can't directly query from browser, but we can suggest the query
  console.log('💡 Run this query to check the task:');
  console.log(`SELECT id, title, assigned_to, provider_id FROM maintenance_tasks WHERE id = '${taskId}';`);
}

// Test a simple command to see the raw AI response
async function testSimpleCommand() {
  console.log('🧪 Testing Simple Command');
  console.log('=' .repeat(30));
  
  const command = 'Fix the door, assign to Ashlee Glass';
  const userId = 'c749ea63-c8cb-4e8b-b428-9a467755408b';
  
  console.log(`Command: "${command}"`);
  console.log(`User ID: ${userId}`);
  
  try {
    const response = await fetch('/functions/v1/ai-command-processor', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('supabase.auth.token')}`,
      },
      body: JSON.stringify({
        command: command,
        userId: userId
      })
    });

    const result = await response.json();
    
    console.log('\n📋 Full AI Response:');
    console.log(JSON.stringify(result, null, 2));
    
    // Analyze the response
    console.log('\n🔍 Analysis:');
    if (result.success) {
      console.log('✅ Request succeeded');
      
      if (result.message && result.message.includes('assigned')) {
        console.log('✅ Message mentions assignment');
      } else {
        console.log('❌ Message does NOT mention assignment');
      }
      
      if (result.entityId) {
        console.log(`📋 Task ID: ${result.entityId}`);
        console.log('💡 Check this task in the UI to see if assignee is set');
      }
    } else {
      console.log('❌ Request failed');
      console.log(`Error: ${result.message}`);
    }
    
    return result;
    
  } catch (error) {
    console.error('💥 Error:', error);
    return null;
  }
}

// Test what happens when we manually check for Ashlee Glass
async function testAssigneeLookup() {
  console.log('👤 Testing Assignee Lookup');
  console.log('=' .repeat(30));
  
  console.log('💡 The Edge Function should be able to find:');
  console.log('   Name: Ashlee Glass');
  console.log('   ID: 4a72122e-c35f-4a51-ae6f-3f085248fc39');
  console.log('   Email: <EMAIL>');
  console.log('   Role: service_provider');
  
  console.log('\n🔍 The lookup query should be:');
  console.log('   SELECT id, first_name, last_name, email, role FROM profiles');
  console.log('   WHERE first_name ILIKE \'%Ashlee Glass%\'');
  console.log('      OR last_name ILIKE \'%Ashlee Glass%\'');
  console.log('      OR email ILIKE \'%Ashlee Glass%\';');
  
  console.log('\n💡 Expected result:');
  console.log('   assigneeName = "Ashlee Glass"');
  console.log('   providerId = "4a72122e-c35f-4a51-ae6f-3f085248fc39"');
}

// Export functions
window.debugAIExtraction = debugAIExtraction;
window.testSimpleCommand = testSimpleCommand;
window.testAssigneeLookup = testAssigneeLookup;

console.log(`
🔍 AI Extraction Debug Tools

Available functions:
• testSimpleCommand() - Test one simple command (recommended)
• debugAIExtraction() - Test multiple commands
• testAssigneeLookup() - Check assignee lookup logic

Example usage:
testSimpleCommand()
`);

// Auto-run simple test
console.log('🔧 Auto-running simple test in 2 seconds...');
setTimeout(() => {
  testSimpleCommand();
}, 2000);
