import { useEffect, useState, memo, useRef, useCallback } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { supabase } from '@/integrations/supabase/client';
import PageTransition from '../components/layout/PageTransition';
import DashboardView from '../components/dashboard/DashboardView';
import AiCommandCenter from '../components/dashboard/AiCommandCenter';
import { useDashboardDataQuery } from '../hooks/useDashboardDataQuery';
import { Loader2, WifiOff, RefreshCcw, Download } from 'lucide-react';
import DashboardWizard from '@/components/onboarding/DashboardWizard';
import { toast } from 'sonner';
import { useQueryClient } from '@tanstack/react-query';
import { Button } from '@/components/ui/button';
import { checkForUpdates } from '@/utils/serviceWorkerRegistration';
import PurchaseOrderDetailsDialog from '@/components/inventory/PurchaseOrderDetailsDialog';
import { useDisclosure } from '@/hooks/useDisclosure';
import { usePurchaseOrders } from '@/hooks/usePurchaseOrders';
import { PurchaseOrder } from '@/types/inventory';
import { syncAllPropertiesCalendarsIfNeeded } from '@/utils/calendarUtils';
import { useAuth } from '@/contexts/AuthContext';
import { retrySupabaseFetch } from '@/utils/cacheUtils';



interface BeforeInstallPromptEvent extends Event {
  prompt: () => Promise<void>;
  userChoice: Promise<{ outcome: 'accepted' | 'dismissed'; platform: string }>;
}

// Use memo to prevent unnecessary re-renders
const DashboardContent = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { authState } = useAuth();
  const userId = authState?.user?.id;
  const {
    properties,
    maintenanceTaskResult,
    inventoryItems,
    damages,
    purchaseOrders,
    loading,
    error,
    refreshData
  } = useDashboardDataQuery();

  // Extract tasks and refresh function for convenience
  const { tasks: maintenanceTasks, refreshTasks } = maintenanceTaskResult || { tasks: [], refreshTasks: () => {} };

  const [installPrompt, setInstallPrompt] = useState<BeforeInstallPromptEvent | null>(null);
  const [canInstall, setCanInstall] = useState(false);
  const [isOffline, setIsOffline] = useState(!navigator.onLine);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // State for purchase order details dialog
  const [selectedOrder, setSelectedOrder] = useState<PurchaseOrder | null>(null);
  const orderDetailsDialog = useDisclosure();
  const { updateOrderStatus, updateOrderItemQuantity, deletePurchaseOrder } = usePurchaseOrders();

  // REMOVED: Visibility change tracking
  // This was causing data to disappear when the app comes back into focus

  // Check online/offline status and update UI accordingly
  useEffect(() => {
    const handleOnline = () => {
      setIsOffline(false);
      // No toast notification
      refreshData();
    };

    const handleOffline = () => {
      setIsOffline(true);
      // No toast notification
    };

    // Set initial state
    setIsOffline(!navigator.onLine);

    // Add event listeners
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [refreshData]);

  // Handle PWA installation
  useEffect(() => {
    // Skip if already in standalone mode (installed)
    if (window.matchMedia('(display-mode: standalone)').matches) {
      setCanInstall(false);
      return;
    }

    const handleBeforeInstallPrompt = (e: BeforeInstallPromptEvent) => {
      // Prevent Chrome 76+ from automatically showing the prompt
      e.preventDefault();
      // Stash the event so it can be triggered later
      setInstallPrompt(e);
      // Update UI to notify the user they can install the PWA
      setCanInstall(true);
    };

    const handleAppInstalled = () => {
      // Hide the install promotion
      setCanInstall(false);
      // Show success message
      toast.success('StayFu app was installed successfully!');
      // Log install to analytics
      console.log('App was installed');
    };

    // Add event listeners
    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt as EventListener);
    window.addEventListener('appinstalled', handleAppInstalled);

    // Check for service worker updates when dashboard loads
    if ('serviceWorker' in navigator) {
      checkForUpdates();
    }

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt as EventListener);
      window.removeEventListener('appinstalled', handleAppInstalled);
    };
  }, []);

  const handleInstallClick = async () => {
    if (!installPrompt) return;

    try {
      // Show the install prompt
      await installPrompt.prompt();

      // Wait for the user to respond to the prompt
      const choiceResult = await installPrompt.userChoice;

      if (choiceResult.outcome === 'accepted') {
        console.log('User accepted the installation');
        toast.success('Installing app...');
        setCanInstall(false);
      } else {
        console.log('User dismissed the installation');
        toast.info('App installation canceled');
      }
    } catch (error) {
      console.error('Error installing app:', error);
      toast.error('Installation failed. Please try again.');
    }
  };

  // Logging is now handled with regular console.log

  // Log data to debug the issue with maintenance tasks
  useEffect(() => {
    if (!loading) {
      console.log('[Dashboard] Data loaded:', {
        properties: properties?.length || 0,
        maintenanceTasks: maintenanceTasks?.length || 0,
        inventoryItems: inventoryItems?.length || 0,
        purchaseOrders: purchaseOrders?.length || 0
      });

      // Log the actual maintenance tasks for debugging
      console.log('[Dashboard] Maintenance tasks:', maintenanceTasks);

      // We'll use the RPC function instead of direct query to prevent infinite loops
      if (userId && maintenanceTasks.length === 0 && !hasTriedRpcRef.current) {
        console.log('[Dashboard] No tasks loaded, trying RPC function...');
        hasTriedRpcRef.current = true;

        // Use our RPC function instead of direct query
        supabase.rpc('get_maintenance_tasks_for_user', {
          p_user_id: userId
        })
        .then(({ data, error }) => {
          if (error) {
            console.error('[Dashboard] RPC function error:', error);
            // Fall back to refreshTasks
            if (refreshTasks) {
              console.log('[Dashboard] Falling back to refreshTasks() after RPC error');
              refreshTasks();
            }
          } else {
            console.log(`[Dashboard] RPC function found ${data?.length || 0} tasks`);
            if (data && data.length > 0 && refreshTasks) {
              console.log('[Dashboard] Calling refreshTasks() with RPC data');
              refreshTasks();
            } else if (refreshTasks) {
              // Even if no tasks found, still refresh to ensure UI is updated
              console.log('[Dashboard] No tasks found with RPC, still refreshing');
              refreshTasks();
            }
          }
        })
        .catch((err: Error) => {
          console.error('[Dashboard] Error with RPC function:', err);
          // Fall back to refreshTasks
          if (refreshTasks) {
            console.log('[Dashboard] Falling back to refreshTasks() after RPC exception');
            refreshTasks();
          }
        });
      }
    }

    // Auto-sync property calendars if needed
    if (!loading && properties?.length > 0 && userId) {
      // Use type assertion to fix type mismatch
      const propertiesWithCorrectType = properties.map((p: any) => ({
        ...p,
        // Add any missing fields required by the Property type from useProperties
        image_url: p.imageUrl || '',
      }));

      syncAllPropertiesCalendarsIfNeeded(propertiesWithCorrectType as any, userId)
        .then(() => {
          console.log('[Dashboard] Auto-synced property calendars if needed');
        })
        .catch(error => {
          console.error('[Dashboard] Error auto-syncing property calendars:', error);
        });
    }
  }, [loading, properties, userId]);

  // Track navigation to detect when we're returning to the dashboard
  const previousPathRef = useRef<string | null>(null);
  const hasInitializedRef = useRef(false);
  const isReturningToDashboardRef = useRef(false);
  const hasTriedRpcRef = useRef(false);

  // Track navigation history and trigger refresh when returning to dashboard
  useEffect(() => {
    const handleLocationChange = () => {
      const currentPath = window.location.pathname;

      // If we're on the dashboard now
      if (currentPath === '/' || currentPath === '/dashboard') {
        // And we were somewhere else before
        if (previousPathRef.current && previousPathRef.current !== '/' && previousPathRef.current !== '/dashboard') {
          console.log(`[Dashboard] Returning to dashboard from ${previousPathRef.current}`);
          isReturningToDashboardRef.current = true;

          // Force a refresh when returning to dashboard
          if (userId) {
            console.log('[Dashboard] Forcing refresh due to navigation back to dashboard');

            // Use the direct refresh approach instead of visibility action
            refreshData();

            // Also use the force load approach for maintenance tasks
            setTimeout(() => {
              forceLoadMaintenanceTasks();
            }, 300);
          }
        }
      }

      // Update previous path
      previousPathRef.current = currentPath;
    };

    // Set up listener for browser back/forward navigation
    window.addEventListener('popstate', handleLocationChange);

    // Also listen for hash changes (for hash router)
    window.addEventListener('hashchange', handleLocationChange);

    // Initial check
    handleLocationChange();

    return () => {
      window.removeEventListener('popstate', handleLocationChange);
      window.removeEventListener('hashchange', handleLocationChange);
    };
  }, [userId]);

  // Auto-refresh data when component mounts or when returning to dashboard
  // Use a ref to track if we've already initialized to prevent multiple refreshes
  const hasRefreshedRef = useRef(false);

  useEffect(() => {
    // Only run this effect once when the component mounts or when returning to dashboard
    if (userId && !loading && (!hasInitializedRef.current || isReturningToDashboardRef.current) && !hasRefreshedRef.current) {
      console.log(`[Dashboard] Refreshing data (initialization: ${!hasInitializedRef.current}, returning: ${isReturningToDashboardRef.current})`);
      hasInitializedRef.current = true;
      hasRefreshedRef.current = true;

      // Reset the returning flag
      if (isReturningToDashboardRef.current) {
        isReturningToDashboardRef.current = false;
      }

      // Use a longer delay in production to ensure all hooks are initialized
      const delay = process.env.NODE_ENV === 'production' ? 2000 : 1000;
      console.log(`[Dashboard] Using ${delay}ms delay for refresh (${process.env.NODE_ENV} mode)`);

      const timer = setTimeout(() => {
        refreshData();

        // Only try the direct approach in production if we don't have data
        if (process.env.NODE_ENV === 'production' && maintenanceTasks.length === 0) {
          setTimeout(() => {
            console.log('[Dashboard] Production environment with no tasks, trying direct approach');
            forceLoadMaintenanceTasks();
          }, 3000);
        }
      }, delay);
      return () => clearTimeout(timer);
    }
  }, [userId, loading]); // Removed location.pathname to prevent navigation-based refreshes

  // Separate effect to handle the case when we have no maintenance tasks
  // In production, we'll retry multiple times
  const retryCountRef = useRef(0);
  const MAX_RETRIES = process.env.NODE_ENV === 'production' ? 3 : 1;

  useEffect(() => {
    // Only run this effect after the initial load and if we have no tasks
    // In production, we'll retry multiple times
    if (userId && !loading && hasInitializedRef.current && retryCountRef.current < MAX_RETRIES && maintenanceTasks.length === 0) {
      retryCountRef.current += 1;
      console.log(`[Dashboard] No maintenance tasks found, retry attempt ${retryCountRef.current}/${MAX_RETRIES}`);

      // Increase delay for each retry
      const delay = 2000 + (retryCountRef.current * 1000);
      console.log(`[Dashboard] Using ${delay}ms delay for retry`);

      // Try another refresh after a delay
      const timer = setTimeout(() => {
        console.log(`[Dashboard] Executing retry refresh #${retryCountRef.current}`);
        refreshData();
      }, delay);
      return () => clearTimeout(timer);
    }
  }, [userId, loading, maintenanceTasks.length, refreshData]);

  // Handle notifications and messages from search params
  useEffect(() => {
    const status = searchParams.get('status');
    const message = searchParams.get('message');
    const taskId = searchParams.get('taskId');

    if (status && message) {
      if (status === 'success') {
        toast.success(message);
      } else if (status === 'error') {
        toast.error(message);
      } else if (status === 'info') {
        toast.info(message);
      }

      // Clean up URL params after processing
      const newUrl = window.location.pathname;
      window.history.replaceState({}, document.title, newUrl);

      if (taskId) {
        refreshData();
      }
    }
  }, [searchParams, refreshData]);



  // Simplified refresh function that works with our global refresh system
  const handleRefreshData = async () => {
    console.log('[Dashboard] Manual refresh triggered');

    // Only proceed if not already refreshing
    if (isRefreshing) {
      console.log('[Dashboard] Already refreshing, skipping duplicate refresh');
      return;
    }

    setIsRefreshing(true);

    // No toast notification

    try {
      // Check network status
      const isOffline = typeof navigator !== 'undefined' && 'onLine' in navigator && !navigator.onLine;
      if (isOffline) {
        console.log('[Dashboard] Network is offline, using cached data');
        // No toast notification
        setIsRefreshing(false);
        return;
      }

      // Call the hook's refresh function directly
      console.log('[Dashboard] Calling refreshData() function directly');
      await refreshData();
      console.log('[Dashboard] Data refresh completed');

      // Allow a short moment for the UI to update before checking for missing data
      await new Promise(resolve => setTimeout(resolve, 300));

      // If we still don't have maintenance tasks, try a more aggressive approach
      if (maintenanceTasks.length === 0) {
        console.log('[Dashboard] Still no maintenance tasks after refresh, trying direct approach');
        await forceLoadMaintenanceTasks();
      }

      // No toast notification
    } catch (error) {
      console.error('[Dashboard] Error refreshing data:', error);

      // Even if refreshData fails, try more aggressive approach for maintenance tasks
      // but only if we don't already have data
      if (maintenanceTasks.length === 0) {
        try {
          console.log('[Dashboard] Still no maintenance tasks after refresh failure, trying direct approach');
          await forceLoadMaintenanceTasks();

          // Check if we got any tasks after force load
          if (maintenanceTasks.length > 0) {
            // No toast notification
          } else {
            // No toast notification
          }
        } catch (forceLoadError) {
          console.error('[Dashboard] Error in force load maintenance tasks:', forceLoadError);
          // No toast notification
        }
      } else {
        // No toast notification
      }
    } finally {
      setIsRefreshing(false);
    }
  };

  // Special function to force-load maintenance tasks directly
  const forceLoadMaintenanceTasks = async () => {
    console.log('[Dashboard] Force-loading maintenance tasks directly');

    try {
      // First, try our new RPC function with retry mechanism
      try {
        console.log('[Dashboard] Trying get_maintenance_tasks_for_user RPC function with retry');

        // Use our retry mechanism for Supabase fetch operations
        const { data: rpcData, error: rpcError } = await retrySupabaseFetch(async () => {
          return await supabase.rpc('get_maintenance_tasks_for_user', {
            p_user_id: userId
          });
        });

        if (rpcError) {
          console.error('[Dashboard] Error with get_maintenance_tasks_for_user RPC:', rpcError);
          // Continue to fallback methods
        } else if (rpcData && rpcData.length > 0) {
          console.log(`[Dashboard] RPC function found ${rpcData.length} tasks`);

          // We need to manually update the dashboard data
          if (refreshTasks) {
            console.log('[Dashboard] Calling refreshTasks() to update UI with RPC data');
            refreshTasks();

            // Force a re-render by updating the URL with a timestamp
            const timestamp = Math.floor(Date.now() / 1000); // Use seconds instead of milliseconds
            window.history.replaceState(
              {},
              document.title,
              `${window.location.pathname}?refresh=${timestamp}`
            );

            return; // Success, no need to try other approaches
          }
        }
      } catch (rpcErr) {
        console.error('[Dashboard] Exception with get_maintenance_tasks_for_user RPC:', rpcErr);
        // Continue to fallback methods
      }

      // Check if user is a staff member
      const isStaff = authState?.profile?.role === 'staff';

      if (isStaff && process.env.NODE_ENV === 'production') {
        console.log('[Dashboard] User is staff, using team-specific approach');

        // Get team memberships
        const { data: teamMemberships } = await supabase
          .from('team_members')
          .select('team_id')
          .eq('user_id', userId)
          .eq('status', 'active');

        if (teamMemberships && teamMemberships.length > 0) {
          const teamId = teamMemberships[0].team_id;
          console.log(`[Dashboard] Staff is member of team ${teamId}, using team-specific Edge Function`);

          // Use the team-specific Edge Function
          const { data: teamEdgeData, error: teamEdgeError } = await supabase.functions.invoke('get-team-maintenance-tasks', {
            body: { userId, teamId }
          });

          if (teamEdgeError) {
            console.error('[Dashboard] Team Edge function error:', teamEdgeError);
          } else if (teamEdgeData && teamEdgeData.length > 0) {
            console.log(`[Dashboard] Team Edge function found ${teamEdgeData.length} tasks`);

            // We need to manually update the dashboard data
            if (refreshTasks) {
              console.log('[Dashboard] Calling refreshTasks() to update UI with team data');
              refreshTasks();

              // Force a re-render by updating the URL with a timestamp
              const timestamp = Math.floor(Date.now() / 1000); // Use seconds instead of milliseconds
              window.history.replaceState(
                {},
                document.title,
                `${window.location.pathname}?refresh=${timestamp}`
              );

              return; // Success, no need to try other approaches
            }
          }
        }
      }

      // Direct query approach (fallback) - but only if we don't already have tasks
      if (maintenanceTasks.length === 0) {
        try {
          const { data, error } = await supabase.from('maintenance_tasks').select('*');

          if (error) {
            console.error('[Dashboard] Force-load error:', error);
            return;
          }

          if (data && data.length > 0) {
            console.log(`[Dashboard] Force-loaded ${data.length} maintenance tasks directly`);

            // We need to manually update the dashboard data
            if (refreshTasks) {
              console.log('[Dashboard] Calling refreshTasks() to update UI');
              refreshTasks();
              return; // Success, no need to continue
            }
          } else {
            console.log('[Dashboard] No maintenance tasks found in direct query');
          }
        } catch (queryErr) {
          console.error('[Dashboard] Error in direct query:', queryErr);
        }
      } else {
        console.log('[Dashboard] Already have maintenance tasks, skipping direct query');
      }

      // In production, if we still can't get tasks, try one more extreme approach
      if (process.env.NODE_ENV === 'production' && maintenanceTasks.length === 0) {
        console.log('[Dashboard] Production environment with no tasks, trying service role query');

        // Try to use the Supabase Edge Function to get tasks
        try {
          const { data: edgeData, error: edgeError } = await supabase.functions.invoke('get-maintenance-tasks', {
            body: { userId }
          });

          if (edgeError) {
            console.error('[Dashboard] Edge function error:', edgeError);
          } else if (edgeData && edgeData.length > 0) {
            console.log(`[Dashboard] Edge function found ${edgeData.length} tasks`);
            if (refreshTasks) {
              console.log('[Dashboard] Calling refreshTasks() with Edge Function data');
              refreshTasks();

              // Force a re-render by updating the URL with a timestamp
              const timestamp = Math.floor(Date.now() / 1000); // Use seconds instead of milliseconds
              window.history.replaceState(
                {},
                document.title,
                `${window.location.pathname}?refresh=${timestamp}`
              );
            }
          }
        } catch (edgeErr) {
          console.error('[Dashboard] Error calling edge function:', edgeErr);
        }
      }
    } catch (err) {
      console.error('[Dashboard] Error in force-load:', err);
    }
  };

  const handleViewMore = (section: 'properties' | 'maintenance' | 'inventory' | 'damages' | 'purchaseOrders') => {
    switch (section) {
      case 'properties':
        navigate('/properties');
        break;
      case 'maintenance':
        navigate('/maintenance');
        break;
      case 'inventory':
        navigate('/inventory');
        break;
      case 'damages':
        navigate('/damages');
        break;
      case 'purchaseOrders':
        navigate('/purchase-orders');
        break;
    }
  };

  const handleViewOrder = (order: PurchaseOrder) => {
    console.log('[Dashboard] Opening purchase order details:', order.id);
    setSelectedOrder(order);
    orderDetailsDialog.onOpen();
  };

  const handleUpdateOrderStatus = (orderId: string, status: any) => {
    updateOrderStatus.mutate({ orderId, status });
  };

  const handleUpdateOrderItemQuantity = (orderId: string, itemId: string, quantity: number) => {
    updateOrderItemQuantity.mutate({ orderId, itemId, quantity });
  };

  const handleDeleteOrder = (orderId: string) => {
    deletePurchaseOrder.mutate(orderId);
    orderDetailsDialog.onClose();
  };

  const handleCheckout = (orderId: string) => {
    updateOrderStatus.mutate({
      orderId,
      status: 'ordered'
    });
  };



  return (
    <>
      {/* Modern Dashboard Background with Glassmorphism */}
      <div className="dashboard-background">
        <div className="max-w-7xl mx-auto px-2 sm:px-3 lg:px-4 py-1">
        {/* Ultra-Compact Header with Inline AI Assistant */}
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-3">
            <h1 className="text-base font-bold">Dashboard</h1>
            {isOffline && (
              <div className="flex items-center text-amber-600 bg-amber-50 dark:bg-amber-950/50 dark:text-amber-400 px-1.5 py-0.5 rounded text-xs">
                <WifiOff className="h-3 w-3 mr-1" />
                <span>Offline</span>
              </div>
            )}
          </div>

          <div className="flex items-center gap-1">
            {canInstall && (
              <Button size="sm" variant="outline" onClick={handleInstallClick} className="h-7 px-2 text-xs">
                <Download className="h-3 w-3 mr-1" />
                Install
              </Button>
            )}
            <Button
              size="sm"
              variant="outline"
              onClick={handleRefreshData}
              disabled={loading || isRefreshing}
              className="h-7 px-2 text-xs"
            >
              {isRefreshing ? (
                <Loader2 className="h-3 w-3 animate-spin" />
              ) : (
                <RefreshCcw className="h-3 w-3" />
              )}
            </Button>
          </div>
        </div>

        {/* Inline AI Assistant - Ultra Compact */}
        <AiCommandCenter />

        {/* Debug info for development */}
        {process.env.NODE_ENV === 'development' && (
          <div className="p-4 mb-4 border border-yellow-400 bg-yellow-50 rounded-md">
            <h3 className="font-bold">Debug Info:</h3>
            <div className="text-sm">
              <p>Loading: {loading ? 'true' : 'false'}</p>
              <p>Refreshing: {isRefreshing ? 'true' : 'false'}</p>
              <p>Network: {isOffline ? 'Offline' : 'Online'}</p>
              <p>Visibility: {document.visibilityState}</p>
              <p>Error: {error || 'none'}</p>
              <p>Properties: {properties?.length || 0}</p>
              <p>Maintenance Tasks: {maintenanceTasks?.length || 0}</p>
              <p>Critical Tasks: {maintenanceTasks?.filter((task: any) => task && (task.severity === 'critical' || task.severity === 'high'))?.length || 0}</p>
              <p>Inventory Items: {inventoryItems?.length || 0}</p>
              <p>Low Stock Items: {inventoryItems?.filter((item: any) => item && typeof item.quantity === 'number' && typeof item.minQuantity === 'number' && item.quantity <= item.minQuantity)?.length || 0}</p>
              <p>Pending Orders: {purchaseOrders?.filter((order: any) => order && order.status === 'pending')?.length || 0}</p>
              <div className="flex gap-2 mt-2">
                <button
                  onClick={handleRefreshData}
                  className="px-2 py-1 bg-blue-500 text-white rounded text-xs"
                  disabled={isRefreshing}
                >
                  Force Refresh
                </button>

              </div>
            </div>
          </div>
        )}

        {error ? (
          <div className="rounded-lg p-4 bg-red-50 border border-red-200 text-red-700 mb-6">
            <h2 className="text-lg font-semibold mb-2">Error Loading Dashboard</h2>
            <p className="text-muted-foreground">{error}</p>
            <button
              onClick={handleRefreshData}
              className="mt-4 text-sm bg-primary text-white px-4 py-2 rounded-md hover:bg-primary/90"
              disabled={isRefreshing}
            >
              {isRefreshing ? (
                <span className="flex items-center gap-2">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  Trying Again...
                </span>
              ) : (
                'Try Again'
              )}
            </button>
          </div>
        ) : (
          <>
            <DashboardView
              properties={properties.map(p => ({
                ...p,
                imageUrl: p.image_url,
                collections: p.collections || []
              }))}
              maintenanceTasks={maintenanceTasks.map(task => ({
                ...task,
                propertyName: task.property_name,
                dueDate: task.due_date,
                createdAt: task.created_at
              }))}
              inventoryItems={inventoryItems.map(item => ({
                ...item,
                propertyId: item.property_id,
                propertyName: item.property_name,
                minQuantity: item.min_quantity,
                collection: ''
              }))}
              damages={damages.map(damage => ({
                id: damage.id,
                title: damage.title,
                propertyName: damage.property_name,
                status: damage.status === 'open' ? 'new' :
                       damage.status === 'in_progress' ? 'pending' : 'completed',
                reportedAt: new Date(damage.created_at).toLocaleDateString()
              }))}
              purchaseOrders={purchaseOrders.map(order => {
                // Map to a valid POStatus type
                let mappedStatus: 'pending' | 'ordered' | 'delivered' | 'archived';

                switch(order.status) {
                  case 'cancelled':
                    mappedStatus = 'archived';
                    break;
                  case 'draft':
                    mappedStatus = 'pending';
                    break;
                  case 'received':
                    mappedStatus = 'delivered';
                    break;
                  default:
                    mappedStatus = order.status as any;
                }

                return {
                  id: order.id,
                  status: mappedStatus,
                  total: order.total,
                  items: order.items || [],
                  property_id: order.property_id,
                  property_name: order.property_name,
                  created_at: order.created_at,
                  updated_at: order.updated_at,
                  user_id: '',
                  is_archived: false
                };
              })}
              isLoading={loading || isRefreshing}
              onViewMore={handleViewMore}
              onViewOrder={handleViewOrder}
            />

            {/* Purchase Order Details Dialog */}
            {selectedOrder && (
              <PurchaseOrderDetailsDialog
                isOpen={orderDetailsDialog.isOpen}
                onClose={orderDetailsDialog.onClose}
                order={selectedOrder}
                onDelete={handleDeleteOrder}
                onCheckout={handleCheckout}
                onUpdateStatus={handleUpdateOrderStatus}
                onEditQuantity={handleUpdateOrderItemQuantity}
              />
            )}
          </>
        )}

        <DashboardWizard />
        </div>
      </div>
    </>
  );
};

const Dashboard = memo(() => {
  return (
    <PageTransition>
      <DashboardContent />
    </PageTransition>
  );
});

export default Dashboard;
