// This script initializes the invitation in the database
// Run it with: node initialize-invitation.js
// NOTE: This is a development setup script. For production, use environment variables.

require('dotenv').config(); // Load environment variables
const { createClient } = require('@supabase/supabase-js');

// Supabase configuration - prefer environment variables, fallback to development values
const SUPABASE_URL = process.env.VITE_SUPABASE_URL || 'https://pwaeknalhosfwuxkpaet.supabase.co';
const SUPABASE_KEY = process.env.VITE_SUPABASE_PUBLISHABLE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.Nh0Qs9jXFXYwYpl-lBBKUKR4LCxQS_5RYGIk9sNfGEY';

// Create Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

// Invitation details
const invitationData = {
  id: '74b23b1b-28c4-4448-8ae1-8b76fcfe30c6',
  team_id: '3b9e7651-68c3-432f-9a28-7440139250f3',
  email: '<EMAIL>',
  invited_by: 'c749ea63-c8cb-4e8b-b428-9a467755408b',
  role: 'service_provider',
  token: '8b8e82d9-cb6e-403c-b609-17dea7c1df34',
  status: 'pending',
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
  expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days from now
  team_name: 'All Properties'
};

// Team details
const teamData = {
  id: '3b9e7651-68c3-432f-9a28-7440139250f3',
  name: 'All Properties',
  owner_id: 'c749ea63-c8cb-4e8b-b428-9a467755408b',
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString()
};

async function initializeInvitation() {
  console.log('Initializing invitation...');
  
  // First, check if the team exists
  const { data: existingTeam, error: teamError } = await supabase
    .from('teams')
    .select('*')
    .eq('id', teamData.id)
    .single();
    
  if (teamError && teamError.code !== 'PGRST116') {
    console.error('Error checking team:', teamError);
    return;
  }
  
  // If team doesn't exist, create it
  if (!existingTeam) {
    console.log('Creating team...');
    const { error: createTeamError } = await supabase
      .from('teams')
      .upsert(teamData);
      
    if (createTeamError) {
      console.error('Error creating team:', createTeamError);
      return;
    }
    
    console.log('Team created successfully');
  } else {
    console.log('Team already exists');
  }
  
  // Check if invitation exists
  const { data: existingInvitation, error: invitationError } = await supabase
    .from('team_invitations')
    .select('*')
    .eq('token', invitationData.token)
    .single();
    
  if (invitationError && invitationError.code !== 'PGRST116') {
    console.error('Error checking invitation:', invitationError);
    return;
  }
  
  // If invitation doesn't exist, create it
  if (!existingInvitation) {
    console.log('Creating invitation...');
    const { error: createInvitationError } = await supabase
      .from('team_invitations')
      .upsert(invitationData);
      
    if (createInvitationError) {
      console.error('Error creating invitation:', createInvitationError);
      return;
    }
    
    console.log('Invitation created successfully');
  } else {
    console.log('Invitation already exists');
    
    // Update the invitation to ensure it's pending and has the correct team name
    console.log('Updating invitation...');
    const { error: updateInvitationError } = await supabase
      .from('team_invitations')
      .update({
        status: 'pending',
        team_name: 'All Properties',
        expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString() // 7 days from now
      })
      .eq('token', invitationData.token);
      
    if (updateInvitationError) {
      console.error('Error updating invitation:', updateInvitationError);
      return;
    }
    
    console.log('Invitation updated successfully');
  }
  
  console.log('Initialization complete!');
}

// Run the initialization
initializeInvitation()
  .catch(error => {
    console.error('Initialization failed:', error);
  });
